#!/bin/bash

# Predator Wallet Monitor - Docker Stop Script

echo "🛑 Stopping Predator Wallet Monitor..."

# Use docker compose if available, otherwise fall back to docker-compose
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker compose"
else
    DOCKER_COMPOSE_CMD="docker-compose"
fi

# Stop the container
$DOCKER_COMPOSE_CMD down

if [ $? -eq 0 ]; then
    echo "✅ Predator Wallet Monitor stopped successfully!"
else
    echo "❌ Failed to stop the container."
fi

# Show final status
echo ""
echo "📊 Container status:"
$DOCKER_COMPOSE_CMD ps
