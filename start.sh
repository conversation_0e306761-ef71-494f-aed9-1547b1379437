#!/bin/bash

# Predator Wallet Monitor - Docker Startup Script

echo "🚀 Starting Predator Wallet Monitor in Docker..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create .env file with your configuration."
    exit 1
fi

echo "🔧 Building Docker image..."

# Use docker compose if available, otherwise fall back to docker-compose
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker compose"
else
    DOCKER_COMPOSE_CMD="docker-compose"
fi

# Build and start the container
$DOCKER_COMPOSE_CMD up --build -d

if [ $? -eq 0 ]; then
    echo "✅ Predator Wallet Monitor is now running in Docker!"
    echo ""
    echo "📋 Useful commands:"
    echo "  View logs:     $DOCKER_COMPOSE_CMD logs -f"
    echo "  Stop:          $DOCKER_COMPOSE_CMD down"
    echo "  Restart:       $DOCKER_COMPOSE_CMD restart"
    echo "  Status:        $DOCKER_COMPOSE_CMD ps"
    echo "  Shell access:  $DOCKER_COMPOSE_CMD exec predator-wallet-monitor bash"
    echo ""
    echo "🔍 Checking container status..."
    $DOCKER_COMPOSE_CMD ps
    echo ""
    echo "📊 Recent logs:"
    $DOCKER_COMPOSE_CMD logs --tail=20
else
    echo "❌ Failed to start the container. Check the logs for details."
    $DOCKER_COMPOSE_CMD logs
fi
