#!/bin/bash

# Predator Wallet Monitor - Management Script

# Use docker compose if available, otherwise fall back to docker-compose
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker compose"
else
    DOCKER_COMPOSE_CMD="docker-compose"
fi

case "$1" in
    start)
        echo "🚀 Starting Predator Wallet Monitor..."
        $DOCKER_COMPOSE_CMD up -d
        ;;
    stop)
        echo "🛑 Stopping Predator Wallet Monitor..."
        $DOCKER_COMPOSE_CMD down
        ;;
    restart)
        echo "🔄 Restarting Predator Wallet Monitor..."
        $DOCKER_COMPOSE_CMD restart
        ;;
    logs)
        echo "📊 Showing logs (Ctrl+C to exit)..."
        $DOCKER_COMPOSE_CMD logs -f
        ;;
    status)
        echo "📋 Container status:"
        $DOCKER_COMPOSE_CMD ps
        echo ""
        echo "🏥 Health status:"
        docker inspect predator-wallet-monitor --format='{{.State.Health.Status}}' 2>/dev/null || echo "Health check not available"
        ;;
    shell)
        echo "🐚 Opening shell in container..."
        $DOCKER_COMPOSE_CMD exec predator-wallet-monitor bash
        ;;
    build)
        echo "🔧 Rebuilding container..."
        $DOCKER_COMPOSE_CMD build --no-cache
        ;;
    update)
        echo "🔄 Updating and rebuilding..."
        $DOCKER_COMPOSE_CMD down
        $DOCKER_COMPOSE_CMD build --no-cache
        $DOCKER_COMPOSE_CMD up -d
        ;;
    clean)
        echo "🧹 Cleaning up..."
        $DOCKER_COMPOSE_CMD down
        docker system prune -f
        ;;
    *)
        echo "🦁 Predator Wallet Monitor - Docker Management"
        echo ""
        echo "Usage: $0 {start|stop|restart|logs|status|shell|build|update|clean}"
        echo ""
        echo "Commands:"
        echo "  start   - Start the container"
        echo "  stop    - Stop the container"
        echo "  restart - Restart the container"
        echo "  logs    - View live logs"
        echo "  status  - Show container status"
        echo "  shell   - Open shell in container"
        echo "  build   - Rebuild the container"
        echo "  update  - Stop, rebuild, and start"
        echo "  clean   - Stop and clean up Docker resources"
        echo ""
        exit 1
        ;;
esac
