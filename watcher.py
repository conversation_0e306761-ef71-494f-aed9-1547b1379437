import time
from web3 import Web3
from utils import (
    init_web3,
    load_wallets,
    send_telegram_message,
    format_eth_amount
)
from config import (
    DESTINATION_WALLET,
    CHECK_INTERVAL,
    MINIMUM_BALANCE,
    GAS_PRICE_GWEI,
    GAS_LIMIT,
    NETWORKS
)

def check_and_withdraw():
    try:
        wallets = load_wallets()
    except Exception as e:
        send_telegram_message(f"❌ Error loading wallets: {str(e)}")
        print(f"Error loading wallets: {e}")
        return
    if not wallets:
        send_telegram_message("⚠️ No wallets loaded! Check your .env and wallet file.")
        print("No wallets loaded!")
        return
    for network in NETWORKS:
        try:
            web3 = init_web3(network["rpc_url"])
            explorer = network["explorer"]
            network_name = network["name"]
        except Exception as e:
            send_telegram_message(f"❌ Error initializing {network_name}: {str(e)}")
            print(f"Error initializing {network_name}: {e}")
            continue
        for wallet in wallets:
            try:
                address = wallet['address']
                balance = web3.eth.get_balance(address)
                print(f"{network_name} {address} balance: {balance}")
                if balance > web3.to_wei(MINIMUM_BALANCE, 'ether'):
                    gas_price = web3.to_wei(GAS_PRICE_GWEI, 'gwei')
                    gas_cost = gas_price * GAS_LIMIT
                    amount_to_send = balance - gas_cost
                    if amount_to_send <= 0:
                        send_telegram_message(f"⚠️ {network_name} {address} has balance but not enough for gas.")
                        continue
                    tx = {
                        'nonce': web3.eth.get_transaction_count(address),
                        'to': DESTINATION_WALLET,
                        'value': amount_to_send,
                        'gas': GAS_LIMIT,
                        'gasPrice': gas_price,
                        'chainId': web3.eth.chain_id
                    }
                    signed_tx = web3.eth.account.sign_transaction(
                        tx,
                        private_key=wallet['private_key']
                    )
                    tx_hash = web3.eth.send_raw_transaction(signed_tx.rawTransaction)
                    msg = (
                        f"🚨 Withdrawal Alert!\n\n"
                        f"Network: {network_name}\n"
                        f"From: {address}\n"
                        f"Amount: {format_eth_amount(amount_to_send)}\n"
                        f"Transaction: {explorer}{tx_hash.hex()}"
                    )
                    send_telegram_message(msg)
                else:
                    print(f"{network_name} {address} balance below threshold.")
            except Exception as e:
                error_msg = f"❌ Error processing wallet {wallet.get('name','')} {address} on {network_name}: {str(e)}"
                send_telegram_message(error_msg)
                print(error_msg)

def main():
    print("Starting wallet monitoring...")
    send_telegram_message("🟢 Predator Wallet Monitor started!")
    while True:
        try:
            check_and_withdraw()
        except Exception as e:
            error_msg = f"❌ Critical error in monitoring loop: {str(e)}"
            send_telegram_message(error_msg)
            print(error_msg)
        time.sleep(CHECK_INTERVAL)

if __name__ == "__main__":
    main()
