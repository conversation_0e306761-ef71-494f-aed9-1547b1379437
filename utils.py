import os
import json
from cryptography.fernet import Fernet
from web3 import Web3
import requests
from eth_account import Account

from config import (
    TELEGRAM_BOT_TOKEN,
    TELEGRAM_CHAT_ID,
    WALLET_FILE,
    ENCRYPTION_KEY,
    NETWORKS
)
from eth_account.hdaccount import ETHEREUM_DEFAULT_PATH

Account.enable_unaudited_hdwallet_features()

def init_web3(rpc_url):
    """Initialize Web3 connection for a given RPC URL"""
    return Web3(Web3.HTTPProvider(rpc_url))

def send_telegram_message(message):
    """Send message via Telegram bot"""
    url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
    data = {"chat_id": TELEGRAM_CHAT_ID, "text": message, "parse_mode": "HTML"}
    try:
        response = requests.post(url, data=data)
        response.raise_for_status()
        return True
    except Exception as e:
        print(f"Error sending Telegram message: {e}")
        return False

def derive_private_key_from_mnemonic(mnemonic, derivation_path=ETHEREUM_DEFAULT_PATH):
    try:
        acct = Account.from_mnemonic(mnemonic, account_path=derivation_path)
        return acct.key.hex(), acct.address
    except Exception as e:
        print(f"Error deriving key from mnemonic: {e}")
        return None, None

def load_env_wallets():
    wallets_env = os.getenv('WALLETS')
    if not wallets_env:
        return []
    try:
        wallets = json.loads(wallets_env)
        result = []
        for w in wallets:
            if w.get("mnemonic"):
                # Derive for each network (same derivation path, but can be extended)
                for net in NETWORKS:
                    priv, addr = derive_private_key_from_mnemonic(w["mnemonic"])
                    if priv and addr:
                        result.append({
                            "address": addr,
                            "private_key": priv,
                            "name": f"{w.get('name', '')} ({net['name']})"
                        })
            elif w.get("address") and w.get("private_key"):
                result.append({
                    "address": w["address"],
                    "private_key": w["private_key"],
                    "name": w.get("name", "")
                })
        return result
    except Exception as e:
        print(f"Error parsing WALLETS from .env: {e}")
        return []

def load_wallets():
    """Load encrypted wallets from file"""
    # Load from encrypted file
    try:
        fernet = Fernet(ENCRYPTION_KEY.encode())
        with open(WALLET_FILE, 'rb') as f:
            encrypted_data = f.read()
        decrypted_data = fernet.decrypt(encrypted_data)
        file_wallets = json.loads(decrypted_data)
    except Exception:
        file_wallets = []
    # Load from .env
    env_wallets = load_env_wallets()
    # Merge, avoid duplicates by address
    all_wallets = {w['address'].lower(): w for w in file_wallets}
    for w in env_wallets:
        all_wallets[w['address'].lower()] = w
    return list(all_wallets.values())

def save_wallets(wallets):
    """Save encrypted wallets to file"""
    try:
        fernet = Fernet(ENCRYPTION_KEY.encode())
        encrypted_data = fernet.encrypt(json.dumps(wallets).encode())
        with open(WALLET_FILE, 'wb') as f:
            f.write(encrypted_data)
        return True
    except Exception as e:
        print(f"Error saving wallets: {e}")
        return False

def add_wallet(private_key, name=""):
    """Add a new wallet to the encrypted storage"""
    wallets = load_wallets()
    # Use Ethereum Mainnet for address derivation
    web3 = init_web3(NETWORKS[0]["rpc_url"])
    account = web3.eth.account.from_key(private_key)
    
    wallet_data = {
        "address": account.address,
        "private_key": private_key,
        "name": name
    }
    
    wallets.append(wallet_data)
    return save_wallets(wallets)

def format_eth_amount(wei_amount):
    """Convert Wei to ETH and format for display"""
    # Use 18 decimals for all EVM chains
    eth_amount = Web3.from_wei(wei_amount, 'ether')
    return f"{eth_amount:.6f}"
