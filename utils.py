import os
import json
from cryptography.fernet import Fernet
from web3 import Web3
import requests
from eth_account import Account

from config import (
    TELEGRAM_BOT_TOKEN,
    TELEGRAM_CHAT_ID,
    WALLET_FILE,
    ENCRYPTION_KEY,
    NETWORKS,
    PRICE_API_URL
)
from eth_account.hdaccount import ETHEREUM_DEFAULT_PATH

Account.enable_unaudited_hdwallet_features()

def init_web3(rpc_url):
    """Initialize Web3 connection for a given RPC URL"""
    return Web3(Web3.HTTPProvider(rpc_url))

def send_telegram_message(message):
    """Send message via Telegram bot"""
    url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
    data = {"chat_id": TELEGRAM_CHAT_ID, "text": message, "parse_mode": "HTML"}
    try:
        response = requests.post(url, data=data)
        response.raise_for_status()
        return True
    except Exception as e:
        print(f"Error sending Telegram message: {e}")
        return False

def derive_private_key_from_mnemonic(mnemonic, derivation_path=ETHEREUM_DEFAULT_PATH):
    try:
        acct = Account.from_mnemonic(mnemonic, account_path=derivation_path)
        return acct.key.hex(), acct.address
    except Exception as e:
        print(f"Error deriving key from mnemonic: {e}")
        return None, None

def load_env_wallets():
    wallets_env = os.getenv('WALLETS')
    if not wallets_env:
        return []
    try:
        wallets = json.loads(wallets_env)
        result = []
        for w in wallets:
            if w.get("mnemonic"):
                # Derive address from mnemonic
                priv, addr = derive_private_key_from_mnemonic(w["mnemonic"])
                if priv and addr:
                    result.append({
                        "address": addr,
                        "private_key": priv,
                        "name": w.get('name', 'Mnemonic Wallet')
                    })
                    print(f"✅ Loaded mnemonic wallet: {w.get('name', 'Mnemonic Wallet')} -> {addr}")
            elif w.get("private_key"):
                # Derive address from private key
                try:
                    web3 = init_web3(NETWORKS[0]["rpc_url"])  # Use first network for derivation
                    account = web3.eth.account.from_key(w["private_key"])
                    result.append({
                        "address": account.address,
                        "private_key": w["private_key"],
                        "name": w.get("name", "Private Key Wallet")
                    })
                    print(f"✅ Loaded private key wallet: {w.get('name', 'Private Key Wallet')} -> {account.address}")
                except Exception as e:
                    print(f"❌ Error deriving address from private key: {e}")
            elif w.get("address") and w.get("private_key"):
                # Legacy format - still supported but address will be re-derived for verification
                try:
                    web3 = init_web3(NETWORKS[0]["rpc_url"])
                    account = web3.eth.account.from_key(w["private_key"])
                    if account.address.lower() == w["address"].lower():
                        result.append({
                            "address": account.address,
                            "private_key": w["private_key"],
                            "name": w.get("name", "Legacy Wallet")
                        })
                        print(f"✅ Verified legacy wallet: {w.get('name', 'Legacy Wallet')} -> {account.address}")
                    else:
                        print(f"❌ Address mismatch for wallet {w.get('name', 'Legacy Wallet')}: expected {account.address}, got {w['address']}")
                except Exception as e:
                    print(f"❌ Error verifying legacy wallet: {e}")
        return result
    except Exception as e:
        print(f"Error parsing WALLETS from .env: {e}")
        return []

def load_wallets():
    """Load encrypted wallets from file"""
    # Load from encrypted file
    try:
        fernet = Fernet(ENCRYPTION_KEY.encode())
        with open(WALLET_FILE, 'rb') as f:
            encrypted_data = f.read()
        decrypted_data = fernet.decrypt(encrypted_data)
        file_wallets = json.loads(decrypted_data)
    except Exception:
        file_wallets = []
    # Load from .env
    env_wallets = load_env_wallets()
    # Merge, avoid duplicates by address
    all_wallets = {w['address'].lower(): w for w in file_wallets}
    for w in env_wallets:
        all_wallets[w['address'].lower()] = w
    return list(all_wallets.values())

def save_wallets(wallets):
    """Save encrypted wallets to file"""
    try:
        fernet = Fernet(ENCRYPTION_KEY.encode())
        encrypted_data = fernet.encrypt(json.dumps(wallets).encode())
        with open(WALLET_FILE, 'wb') as f:
            f.write(encrypted_data)
        return True
    except Exception as e:
        print(f"Error saving wallets: {e}")
        return False

def add_wallet(private_key, name=""):
    """Add a new wallet to the encrypted storage"""
    wallets = load_wallets()
    # Use Ethereum Mainnet for address derivation
    web3 = init_web3(NETWORKS[0]["rpc_url"])
    account = web3.eth.account.from_key(private_key)
    
    wallet_data = {
        "address": account.address,
        "private_key": private_key,
        "name": name
    }
    
    wallets.append(wallet_data)
    return save_wallets(wallets)

# Cache for price data to avoid too many API calls
price_cache = {}
price_cache_timestamp = 0

def get_crypto_prices():
    """Get current crypto prices in USD with caching"""
    global price_cache, price_cache_timestamp
    import time

    current_time = time.time()
    # Cache prices for 60 seconds
    if current_time - price_cache_timestamp < 60 and price_cache:
        return price_cache

    try:
        # Map network names to CoinGecko IDs
        coin_ids = {
            "ethereum": "ethereum",
            "polygon": "matic-network",
            "arbitrum": "ethereum",  # Arbitrum uses ETH
            "binance": "binancecoin"
        }

        ids_param = ",".join(coin_ids.values())
        response = requests.get(f"{PRICE_API_URL}?ids={ids_param}&vs_currencies=usd", timeout=10)
        response.raise_for_status()

        data = response.json()
        price_cache = {
            "ethereum": data.get("ethereum", {}).get("usd", 0),
            "polygon": data.get("matic-network", {}).get("usd", 0),
            "arbitrum": data.get("ethereum", {}).get("usd", 0),  # Same as ETH
            "binance": data.get("binancecoin", {}).get("usd", 0)
        }
        price_cache_timestamp = current_time
        return price_cache

    except Exception as e:
        print(f"Error fetching prices: {e}")
        # Return cached prices or zeros
        return price_cache if price_cache else {
            "ethereum": 0, "polygon": 0, "arbitrum": 0, "binance": 0
        }

def get_network_price_key(network_name):
    """Map network name to price key"""
    network_mapping = {
        "Ethereum Mainnet": "ethereum",
        "Polygon": "polygon",
        "Arbitrum One": "arbitrum",
        "Binance Smart Chain": "binance"
    }
    return network_mapping.get(network_name, "ethereum")

def format_eth_amount(wei_amount, network_name=None):
    """Convert Wei to ETH and format for display with USD value"""
    # Use 18 decimals for all EVM chains
    eth_amount = Web3.from_wei(wei_amount, 'ether')

    if network_name and float(eth_amount) > 0:
        try:
            prices = get_crypto_prices()
            price_key = get_network_price_key(network_name)
            usd_price = prices.get(price_key, 0)
            usd_value = float(eth_amount) * usd_price

            if usd_value > 0:
                return f"{eth_amount:.6f} (${usd_value:.2f})"
        except Exception as e:
            print(f"Error calculating USD value: {e}")

    return f"{eth_amount:.6f}"
