# 🏦 Multi-Account Kraken Integration Setup Guide

## 🚀 Overview

The Enhanced Predator Wallet Monitor now includes **comprehensive multi-account Kraken integration** that:

- ✅ **Monitors ALL balances** across multiple Kraken accounts
- ✅ **$500 minimum threshold** for all assets (automatically calculated)
- ✅ **Automatically withdraws** when balances exceed $500 USD value
- ✅ **Supports 30+ cryptocurrencies** including BTC, ETH, stablecoins, and altcoins
- ✅ **Real-time notifications** with USD values and account names
- ✅ **Safe withdrawal system** using pre-configured addresses per account

## 🔧 Multi-Account Setup Instructions

### 1. Create Kraken API Keys for Each Account

**For each Kraken account you want to monitor:**

1. **Log into your Kraken account**
2. **Go to Settings → API**
3. **Create a new API key** with these permissions:
   - ✅ **Query Funds** (to check balances)
   - ✅ **Withdraw Funds** (for automatic withdrawals)
4. **Copy your API Key and Private Key**
5. **Repeat for all accounts**

### 2. Configure Withdrawal Addresses

**IMPORTANT**: You must pre-configure withdrawal addresses in EACH Kraken account:

1. **Go to Funding → Withdraw**
2. **For each cryptocurrency you want to monitor:**
   - Add your destination wallet address
   - Give it a memorable name (e.g., "btc_withdrawal_key")
   - Complete the verification process
3. **Repeat for all accounts**

### 3. Update Configuration for Multiple Accounts

Edit your `.env` file to include multiple accounts:

```env
# Account 1
KRAKEN_API_KEY_1=your_first_account_api_key
KRAKEN_PRIVATE_KEY_1=your_first_account_private_key
KRAKEN_ACCOUNT_NAME_1=Main Account

# Account 2
KRAKEN_API_KEY_2=your_second_account_api_key
KRAKEN_PRIVATE_KEY_2=your_second_account_private_key
KRAKEN_ACCOUNT_NAME_2=Trading Account

# Account 3
KRAKEN_API_KEY_3=your_third_account_api_key
KRAKEN_PRIVATE_KEY_3=your_third_account_private_key
KRAKEN_ACCOUNT_NAME_3=DCA Account

# Add more accounts as needed (4, 5, 6, etc.)

# Bitcoin destination (already configured)
BTC_DESTINATION_WALLET=******************************************
```

### 4. Configure Withdrawal Keys Per Account

Add withdrawal keys for each account in your `.env` file:

```env
# Account 1 withdrawal keys
KRAKEN_WITHDRAWAL_KEY_1_BTC=main_btc_key
KRAKEN_WITHDRAWAL_KEY_1_ETH=main_eth_key
KRAKEN_WITHDRAWAL_KEY_1_USDT=main_usdt_key

# Account 2 withdrawal keys
KRAKEN_WITHDRAWAL_KEY_2_BTC=trading_btc_key
KRAKEN_WITHDRAWAL_KEY_2_ETH=trading_eth_key
KRAKEN_WITHDRAWAL_KEY_2_USDT=trading_usdt_key

# Account 3 withdrawal keys
KRAKEN_WITHDRAWAL_KEY_3_BTC=dca_btc_key
KRAKEN_WITHDRAWAL_KEY_3_ETH=dca_eth_key
KRAKEN_WITHDRAWAL_KEY_3_USDT=dca_usdt_key

# Add more as needed for each account/asset combination
```

## 💰 $500 Minimum Threshold System

The system monitors **ALL** assets across **ALL** your Kraken accounts with a **universal $500 USD minimum threshold**:

### 🎯 How It Works
- **Every asset** is automatically calculated to reach $500 USD value
- **Real-time price updates** ensure accurate thresholds
- **No manual configuration** needed for individual assets

### 🪙 Example Thresholds (at current prices)
- **BTC**: ~0.0116 BTC (≈$500)
- **ETH**: ~0.2 ETH (≈$500)
- **USDT/USDC**: 500 tokens (≈$500)
- **ADA**: ~1,250 ADA (≈$500)
- **SOL**: ~5 SOL (≈$500)
- **DOT**: ~71 DOT (≈$500)
- **MATIC**: ~556 MATIC (≈$500)
- **AVAX**: ~14 AVAX (≈$500)
- **LINK**: ~36 LINK (≈$500)
- **LTC**: ~7 LTC (≈$500)

### 🔄 Dynamic Calculation
The system automatically calculates minimum balances for **ANY** cryptocurrency:
```
Minimum Balance = $500 ÷ Current Asset Price
```

### 📊 Supported Assets
**ALL** assets available on Kraken are supported, including:
- 🪙 **Major Cryptocurrencies**: BTC, ETH, LTC, BCH, XRP
- 💵 **Stablecoins**: USDT, USDC, DAI, BUSD
- 🚀 **Altcoins**: ADA, SOL, DOT, MATIC, AVAX, LINK, UNI, DOGE
- 🔧 **DeFi Tokens**: AAVE, COMP, MKR, SNX, CRV, SUSHI
- 🌐 **Layer 2 & Scaling**: ATOM, NEAR, FTM, ALGO, FLOW
- **And 100+ more!**

## 🎯 How It Works

### 1. **Continuous Monitoring**
- Checks ALL Kraken balances every 60 seconds
- Compares against minimum thresholds
- Sends notifications when balances detected

### 2. **Smart Withdrawal Process**
1. **Balance Detection** → Immediate notification
2. **Fee Calculation** → Gets current withdrawal fees
3. **Amount Optimization** → Withdraws maximum possible amount
4. **Safe Execution** → Uses official Kraken API
5. **Confirmation** → Success notification with transaction details

### 3. **Safety Features**
- ✅ **5-minute cooldown** between withdrawals per asset
- ✅ **Fee validation** before withdrawal
- ✅ **Error handling** with detailed notifications
- ✅ **Pre-configured addresses** only (no manual entry)

## 📱 Bot Commands

### `/kraken` - Check All Kraken Account Balances
Get real-time summary of ALL your Kraken accounts with USD values and threshold status.

Shows:
- ✅ **All configured accounts**
- ✅ **Significant balances** ($1+ value)
- ✅ **Threshold status** (� above $500, 🟡 below $500)
- ✅ **Account totals** and grand total
- ✅ **Real-time USD values**

## 🔔 Notification Examples

### Balance Detection:
```
💰 KRAKEN BALANCE DETECTED 💰
═══════════════════════════════

🏦 Account: Main Account
💎 Asset: BTC
💰 Balance: 0.******** BTC ($531.09)
💵 Threshold: $500 minimum
⏰ Time: 2025-06-27 15:30:15

🎯 Status: Checking withdrawal options...
```

### Successful Withdrawal:
```
🚨 KRAKEN WITHDRAWAL SUCCESSFUL 🚨
═══════════════════════════════════════

⚡ AUTO-WITHDRAWAL EXECUTED

🏦 Account: Trading Account
💎 Asset: ETH
💰 Amount: 0.******** ETH ($537.50)
💸 Fee: 0.******** ETH ($12.50)
🎯 To: ******************************************
🔗 Ref ID: ABCD-1234-EFGH-5678
⏰ Time: 2025-06-27 15:31:12

✅ Status: Funds secured successfully!
```

### Multi-Account Balance Summary:
```
🏦 KRAKEN MULTI-ACCOUNT BALANCES 🏦
═══════════════════════════════════════

🏦 Main Account:
──────────────────────────────
🟢 BTC: 0.******** BTC ($531.09)
🟡 ETH: 0.******** ETH ($375.00)
🟢 USDT: 750.50 USDT ($750.50)
💰 Account Total: $1,656.59

🏦 Trading Account:
──────────────────────────────
🟢 SOL: 6.25 SOL ($625.00)
🟡 ADA: 800.00 ADA ($320.00)
💰 Account Total: $945.00

🏦 DCA Account:
──────────────────────────────
🟢 DOT: 85.50 DOT ($598.50)
💰 Account Total: $598.50

════════════════════════════════════════
💎 GRAND TOTAL: $3,200.09
💵 Threshold: $500 minimum per asset
🟢 = Above threshold | 🟡 = Below threshold
```

## 🛡️ Security Notes

1. **API Permissions**: Only grant necessary permissions (Query Funds + Withdraw Funds)
2. **Withdrawal Addresses**: Must be pre-configured in Kraken (cannot be changed via API)
3. **Rate Limits**: Built-in cooldowns prevent spam withdrawals
4. **Error Handling**: Comprehensive error checking and notifications
5. **Secure Storage**: API keys stored in environment variables

## 🚀 Getting Started

1. **Complete the multi-account setup steps above**
2. **Restart your Docker container**: `docker-compose restart`
3. **Test with `/kraken` command** to verify all accounts
4. **Monitor notifications** for automatic withdrawals

## 🎯 Example Multi-Account Setup

For 3 Kraken accounts, your `.env` would look like:

```env
# Account 1 - Main Trading
KRAKEN_API_KEY_1=your_main_api_key
KRAKEN_PRIVATE_KEY_1=your_main_private_key
KRAKEN_ACCOUNT_NAME_1=Main Trading
KRAKEN_WITHDRAWAL_KEY_1_BTC=main_btc_key
KRAKEN_WITHDRAWAL_KEY_1_ETH=main_eth_key
KRAKEN_WITHDRAWAL_KEY_1_USDT=main_usdt_key

# Account 2 - DCA Bot
KRAKEN_API_KEY_2=your_dca_api_key
KRAKEN_PRIVATE_KEY_2=your_dca_private_key
KRAKEN_ACCOUNT_NAME_2=DCA Bot
KRAKEN_WITHDRAWAL_KEY_2_BTC=dca_btc_key
KRAKEN_WITHDRAWAL_KEY_2_ETH=dca_eth_key

# Account 3 - Arbitrage
KRAKEN_API_KEY_3=your_arb_api_key
KRAKEN_PRIVATE_KEY_3=your_arb_private_key
KRAKEN_ACCOUNT_NAME_3=Arbitrage
KRAKEN_WITHDRAWAL_KEY_3_BTC=arb_btc_key
KRAKEN_WITHDRAWAL_KEY_3_USDT=arb_usdt_key
```

## 🦁 Multi-Platform Crypto Hunter

Your Enhanced Predator Wallet Monitor now hunts across:
- ✅ **Multiple blockchain wallets** (Ethereum, Polygon, Arbitrum, BSC)
- ✅ **Multiple Kraken accounts** with $500 minimum threshold
- ✅ **ALL cryptocurrencies** automatically supported
- ✅ **Real-time monitoring** every 60 seconds
- ✅ **Automatic withdrawals** to your secure addresses

**The ultimate crypto monitoring and auto-withdrawal system!** 🦁💰🏦✨
