# 🏦 Kraken Integration Setup Guide

## 🚀 Overview

The Enhanced Predator Wallet Monitor now includes **comprehensive Kraken integration** that:

- ✅ **Monitors ALL balances** on your Kraken account
- ✅ **Automatically withdraws** when balances exceed thresholds
- ✅ **Supports 30+ cryptocurrencies** including BTC, ETH, stablecoins, and altcoins
- ✅ **Real-time notifications** with USD values
- ✅ **Safe withdrawal system** using pre-configured addresses

## 🔧 Setup Instructions

### 1. Create Kraken API Keys

1. **Log into your Kraken account**
2. **Go to Settings → API**
3. **Create a new API key** with these permissions:
   - ✅ **Query Funds** (to check balances)
   - ✅ **Withdraw Funds** (for automatic withdrawals)
4. **Copy your API Key and Private Key**

### 2. Configure Withdrawal Addresses

**IMPORTANT**: You must pre-configure withdrawal addresses in your Kraken account:

1. **Go to Funding → Withdraw**
2. **For each cryptocurrency you want to monitor:**
   - Add your destination wallet address
   - Give it a memorable name (e.g., "btc_withdrawal_key")
   - Complete the verification process

### 3. Update Configuration

Edit your `.env` file:

```env
# Kraken API settings
KRAKEN_API_KEY=your_kraken_api_key_here
KRAKEN_PRIVATE_KEY=your_kraken_private_key_here

# Bitcoin destination (already configured)
BTC_DESTINATION_WALLET=******************************************
```

### 4. Configure Withdrawal Keys

Edit `config.py` and update the `KRAKEN_WITHDRAWAL_KEYS` section:

```python
KRAKEN_WITHDRAWAL_KEYS = {
    'BTC': 'your_btc_key_name',      # Name you gave to BTC address in Kraken
    'ETH': 'your_eth_key_name',      # Name you gave to ETH address in Kraken
    'XBT': 'your_btc_key_name',      # Bitcoin (Kraken notation)
    'XETH': 'your_eth_key_name',     # Ethereum (Kraken notation)
    'USDT': 'your_usdt_key_name',    # USDT withdrawal address key
    'USDC': 'your_usdc_key_name',    # USDC withdrawal address key
    # Add more as needed
}
```

## 💰 Supported Assets

The system monitors **ALL** assets in your Kraken account with these default thresholds:

### 🪙 Major Cryptocurrencies
- **BTC/XBT**: 0.0005 (≈$21.50)
- **ETH/XETH**: 0.01 (≈$25.00)
- **LTC**: 0.1 (≈$7.00)
- **BCH**: 0.05 (≈$15.00)
- **XRP**: 20.0 (≈$12.00)

### 💵 Stablecoins
- **USDT**: $10.00
- **USDC**: $10.00
- **DAI**: $10.00
- **BUSD**: $10.00

### 🚀 Altcoins
- **ADA**: 50.0 (≈$20.00)
- **SOL**: 0.1 (≈$10.00)
- **DOT**: 1.0 (≈$7.00)
- **MATIC**: 10.0 (≈$9.00)
- **AVAX**: 0.5 (≈$18.00)
- **LINK**: 1.0 (≈$14.00)
- **UNI**: 1.0 (≈$6.00)
- **DOGE**: 100.0 (≈$8.00)

### 🔧 DeFi Tokens
- **AAVE**: 0.1 (≈$9.00)
- **COMP**: 0.05 (≈$3.00)
- **MKR**: 0.01 (≈$15.00)
- **SNX**: 2.0 (≈$6.00)

**And many more!** The system automatically detects and monitors ANY asset in your account.

## 🎯 How It Works

### 1. **Continuous Monitoring**
- Checks ALL Kraken balances every 60 seconds
- Compares against minimum thresholds
- Sends notifications when balances detected

### 2. **Smart Withdrawal Process**
1. **Balance Detection** → Immediate notification
2. **Fee Calculation** → Gets current withdrawal fees
3. **Amount Optimization** → Withdraws maximum possible amount
4. **Safe Execution** → Uses official Kraken API
5. **Confirmation** → Success notification with transaction details

### 3. **Safety Features**
- ✅ **5-minute cooldown** between withdrawals per asset
- ✅ **Fee validation** before withdrawal
- ✅ **Error handling** with detailed notifications
- ✅ **Pre-configured addresses** only (no manual entry)

## 📱 Bot Commands

### `/kraken` - Check Kraken Balances
Get real-time summary of all your Kraken balances with USD values.

Example output:
```
🏦 KRAKEN BALANCES 🏦
═══════════════════════════
💎 BTC: 0.00123456 BTC ($53.09)
💎 ETH: 0.05678900 ETH ($142.47)
💎 USDT: 25.50 USDT ($25.50)
💎 ADA: 150.75 ADA ($60.30)

💰 Total Value: ~$281.36
```

## 🔔 Notification Examples

### Balance Detection:
```
💰 KRAKEN BALANCE DETECTED 💰
═══════════════════════════════

🏦 Exchange: Kraken
💎 Asset: BTC
💰 Balance: 0.00123456 BTC ($53.09)
⏰ Time: 2025-06-26 18:30:15

🎯 Status: Checking withdrawal options...
```

### Successful Withdrawal:
```
🚨 KRAKEN WITHDRAWAL SUCCESSFUL 🚨
═══════════════════════════════════════

⚡ AUTO-WITHDRAWAL EXECUTED

🏦 Exchange: Kraken
💎 Asset: BTC
💰 Amount: 0.00120000 BTC ($51.60)
💸 Fee: 0.00003456 BTC ($1.49)
🎯 To: ******************************************
🔗 Ref ID: ABCD-1234-EFGH-5678
⏰ Time: 2025-06-26 18:30:45

✅ Status: Funds secured successfully!
```

## 🛡️ Security Notes

1. **API Permissions**: Only grant necessary permissions (Query Funds + Withdraw Funds)
2. **Withdrawal Addresses**: Must be pre-configured in Kraken (cannot be changed via API)
3. **Rate Limits**: Built-in cooldowns prevent spam withdrawals
4. **Error Handling**: Comprehensive error checking and notifications
5. **Secure Storage**: API keys stored in environment variables

## 🚀 Getting Started

1. **Complete the setup steps above**
2. **Restart your Docker container**: `docker-compose restart`
3. **Test with `/kraken` command** to verify connection
4. **Monitor notifications** for automatic withdrawals

Your Enhanced Predator Wallet Monitor will now hunt across **both blockchain wallets AND your Kraken exchange account**! 🦁💰🏦
