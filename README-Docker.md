# 🦁 Predator Wallet Monitor - Docker Setup

## 🚀 Quick Start

### Prerequisites
- **Docker Desktop** installed on your system
  - Windows: [Download Docker Desktop](https://docs.docker.com/desktop/windows/)
  - macOS: [Download Docker Desktop](https://docs.docker.com/desktop/mac/)
  - Linux: [Install Docker Engine](https://docs.docker.com/engine/install/)

### 1. Easy Start (Windows)
```bash
# Double-click start.bat or run in Command Prompt:
start.bat
```

### 2. Easy Start (Linux/macOS)
```bash
# Make scripts executable and run:
chmod +x start.sh
./start.sh
```

### 3. Manual Docker Commands
```bash
# Build and start
docker-compose up --build -d

# View logs
docker-compose logs -f

# Stop
docker-compose down
```

## 📋 Management Commands

### Windows Users
```batch
manage.bat start    # Start the container
manage.bat stop     # Stop the container
manage.bat logs     # View live logs
manage.bat status   # Check status
manage.bat restart  # Restart container
manage.bat shell    # Access container shell
manage.bat update   # Rebuild and restart
```

### Linux/macOS Users
```bash
./manage.sh start    # Start the container
./manage.sh stop     # Stop the container
./manage.sh logs     # View live logs
./manage.sh status   # Check status
./manage.sh restart  # Restart container
./manage.sh shell    # Access container shell
./manage.sh update   # Rebuild and restart
```

## 🔧 Configuration

### Required Files
- `.env` - Your configuration (Telegram bot token, chat ID, etc.)
- `wallets.enc` - Encrypted wallet storage (created automatically)

### Environment Variables
Make sure your `.env` file contains:
```env
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
DESTINATION_WALLET=your_destination_address
# ... other settings
```

## 📊 Monitoring

### Health Checks
The container includes automatic health checks every 5 minutes:
```bash
# Check health status
docker inspect predator-wallet-monitor --format='{{.State.Health.Status}}'
```

### Logs
```bash
# View live logs
docker-compose logs -f

# View last 50 lines
docker-compose logs --tail=50

# View logs for specific time
docker-compose logs --since="2h"
```

### Container Status
```bash
# Check if running
docker-compose ps

# Detailed container info
docker inspect predator-wallet-monitor
```

## 🔄 Auto-Restart

The container is configured with `restart: unless-stopped`, meaning:
- ✅ Automatically restarts if it crashes
- ✅ Starts automatically when Docker starts
- ✅ Survives system reboots
- ❌ Only stops when manually stopped

## 📁 Persistent Data

The following data persists between container restarts:
- `./logs/` - Application logs
- `./wallets.enc` - Encrypted wallet storage
- `./main.log` - Main application log

## 🛠️ Troubleshooting

### Container Won't Start
```bash
# Check Docker is running
docker --version

# Check logs for errors
docker-compose logs

# Rebuild container
docker-compose build --no-cache
```

### Bot Not Responding
```bash
# Check container health
manage.bat status

# View recent logs
docker-compose logs --tail=20

# Restart container
manage.bat restart
```

### Update Application
```bash
# Pull latest changes and rebuild
manage.bat update
```

## 🔒 Security Notes

- Container runs with minimal privileges
- No unnecessary ports exposed
- Logs are rotated automatically (max 10MB, 3 files)
- Sensitive data stored in encrypted files

## 📈 Performance

- **Memory Usage**: ~50-100MB
- **CPU Usage**: Minimal (only during transactions)
- **Network**: Only HTTPS connections to blockchain RPCs and Telegram
- **Storage**: <100MB including logs

## 🎯 Production Ready

This Docker setup is production-ready with:
- ✅ Health checks
- ✅ Automatic restarts
- ✅ Log rotation
- ✅ Persistent storage
- ✅ Resource limits
- ✅ Security best practices
