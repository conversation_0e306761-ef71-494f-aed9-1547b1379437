import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Telegram Configuration
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

# Blockchain Networks Configuration
NETWORKS = [
    {
        "name": "Ethereum Mainnet",
        "rpc_url": "https://ethereum-rpc.publicnode.com",
        "explorer": "https://etherscan.io/tx/"
    },
    {
        "name": "Polygon",
        "rpc_url": "https://polygon-bor-rpc.publicnode.com",
        "explorer": "https://polygonscan.com/tx/"
    },
    {
        "name": "Arbitrum One",
        "rpc_url": "https://arbitrum-one-rpc.publicnode.com",
        "explorer": "https://arbiscan.io/tx/"
    },
    {
        "name": "Binance Smart Chain",
        "rpc_url": "https://bsc-rpc.publicnode.com",
        "explorer": "https://bscscan.com/tx/"
    }
]
DESTINATION_WALLET = os.getenv('DESTINATION_WALLET')
BTC_DESTINATION_WALLET = os.getenv('BTC_DESTINATION_WALLET')

# Wallet Configuration
WALLET_FILE = os.getenv('WALLET_FILE')
ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY')

# Monitoring Configuration
CHECK_INTERVAL = 60  # seconds
MINIMUM_BALANCE = 0.2 # ETH or MATIC or ETH (Arbitrum)
GAS_PRICE_GWEI = 20  # gas price in gwei (lower for better success rate)
GAS_LIMIT = 25000  # higher gas limit for better compatibility

# Kraken API settings
KRAKEN_API_KEY = os.getenv('KRAKEN_API_KEY')
KRAKEN_PRIVATE_KEY = os.getenv('KRAKEN_PRIVATE_KEY')
KRAKEN_API_URL = os.getenv('KRAKEN_API_URL', 'https://api.kraken.com')

# Bitcoin settings
BTC_MINIMUM_BALANCE = float(os.getenv('BTC_MINIMUM_BALANCE', '0.0001'))
BTC_API_URL = os.getenv('BTC_API_URL', 'https://blockstream.info/api')

# Kraken withdrawal settings
KRAKEN_MINIMUM_BALANCES = {
    'BTC': 0.0005,  # Minimum BTC balance to trigger withdrawal
    'ETH': 0.01,    # Minimum ETH balance to trigger withdrawal
    'USDT': 10.0,   # Minimum USDT balance to trigger withdrawal
    'USDC': 10.0,   # Minimum USDC balance to trigger withdrawal
}

# Kraken API settings
KRAKEN_API_KEY = os.getenv('KRAKEN_API_KEY')
KRAKEN_PRIVATE_KEY = os.getenv('KRAKEN_PRIVATE_KEY')
KRAKEN_API_URL = os.getenv('KRAKEN_API_URL', 'https://api.kraken.com')

# Bitcoin settings
BTC_MINIMUM_BALANCE = float(os.getenv('BTC_MINIMUM_BALANCE', '0.0001'))
BTC_API_URL = os.getenv('BTC_API_URL', 'https://blockstream.info/api')
HEALTH_CHECK_INTERVAL = 300  # 5 minutes in seconds

# Price API Configuration
PRICE_API_URL = os.getenv('PRICE_API_URL', 'https://api.coingecko.com/api/v3/simple/price')
