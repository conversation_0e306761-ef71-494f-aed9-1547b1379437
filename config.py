import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Telegram Configuration
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

# Blockchain Networks Configuration
NETWORKS = [
    {
        "name": "Ethereum Mainnet",
        "rpc_url": "https://ethereum-rpc.publicnode.com",
        "explorer": "https://etherscan.io/tx/"
    },
    {
        "name": "Polygon",
        "rpc_url": "https://polygon-bor-rpc.publicnode.com",
        "explorer": "https://polygonscan.com/tx/"
    },
    {
        "name": "Arbitrum One",
        "rpc_url": "https://arbitrum-one-rpc.publicnode.com",
        "explorer": "https://arbiscan.io/tx/"
    },
    {
        "name": "Binance Smart Chain",
        "rpc_url": "https://bsc-rpc.publicnode.com",
        "explorer": "https://bscscan.com/tx/"
    }
]
DESTINATION_WALLET = os.getenv('DESTINATION_WALLET')

# Wallet Configuration
WALLET_FILE = os.getenv('WALLET_FILE')
ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY')

# Monitoring Configuration
CHECK_INTERVAL = 60  # seconds
MINIMUM_BALANCE = 0.01  # ETH or MATIC or ETH (Arbitrum)
GAS_PRICE_GWEI = 50  # gas price in gwei
GAS_LIMIT = 21000  # standard transfer gas limit
