import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Telegram Configuration
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

# Blockchain Networks Configuration
NETWORKS = [
    {
        "name": "Ethereum Mainnet",
        "rpc_url": "https://ethereum-rpc.publicnode.com",
        "explorer": "https://etherscan.io/tx/"
    },
    {
        "name": "Polygon",
        "rpc_url": "https://polygon-bor-rpc.publicnode.com",
        "explorer": "https://polygonscan.com/tx/"
    },
    {
        "name": "Arbitrum One",
        "rpc_url": "https://arbitrum-one-rpc.publicnode.com",
        "explorer": "https://arbiscan.io/tx/"
    },
    {
        "name": "Binance Smart Chain",
        "rpc_url": "https://bsc-rpc.publicnode.com",
        "explorer": "https://bscscan.com/tx/"
    }
]
DESTINATION_WALLET = os.getenv('DESTINATION_WALLET')
BTC_DESTINATION_WALLET = os.getenv('BTC_DESTINATION_WALLET')

# Wallet Configuration
WALLET_FILE = os.getenv('WALLET_FILE')
ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY')

# Monitoring Configuration
CHECK_INTERVAL = 60  # seconds
MINIMUM_BALANCE = 0.2 # ETH or MATIC or ETH (Arbitrum)
GAS_PRICE_GWEI = 20  # gas price in gwei (lower for better success rate)
GAS_LIMIT = 25000  # higher gas limit for better compatibility

# Kraken API settings
KRAKEN_API_KEY = os.getenv('KRAKEN_API_KEY')
KRAKEN_PRIVATE_KEY = os.getenv('KRAKEN_PRIVATE_KEY')
KRAKEN_API_URL = os.getenv('KRAKEN_API_URL', 'https://api.kraken.com')

# Bitcoin settings
BTC_MINIMUM_BALANCE = float(os.getenv('BTC_MINIMUM_BALANCE', '0.0001'))
BTC_API_URL = os.getenv('BTC_API_URL', 'https://blockstream.info/api')

# Kraken withdrawal settings - monitors ALL balances
KRAKEN_MINIMUM_BALANCES = {
    # Major cryptocurrencies
    'BTC': 0.0005,   # Bitcoin
    'ETH': 0.01,     # Ethereum
    'XBT': 0.0005,   # Bitcoin (Kraken notation)
    'XETH': 0.01,    # Ethereum (Kraken notation)

    # Stablecoins
    'USDT': 10.0,    # Tether
    'USDC': 10.0,    # USD Coin
    'DAI': 10.0,     # Dai
    'BUSD': 10.0,    # Binance USD

    # Major altcoins
    'ADA': 50.0,     # Cardano
    'SOL': 0.1,      # Solana
    'DOT': 1.0,      # Polkadot
    'MATIC': 10.0,   # Polygon
    'AVAX': 0.5,     # Avalanche
    'LINK': 1.0,     # Chainlink
    'UNI': 1.0,      # Uniswap
    'LTC': 0.1,      # Litecoin
    'BCH': 0.05,     # Bitcoin Cash
    'XRP': 20.0,     # Ripple
    'DOGE': 100.0,   # Dogecoin
    'SHIB': 1000000, # Shiba Inu

    # DeFi tokens
    'AAVE': 0.1,     # Aave
    'COMP': 0.05,    # Compound
    'MKR': 0.01,     # Maker
    'SNX': 2.0,      # Synthetix
    'CRV': 10.0,     # Curve
    'SUSHI': 5.0,    # SushiSwap

    # Layer 2 & Scaling
    'ATOM': 2.0,     # Cosmos
    'NEAR': 2.0,     # Near Protocol
    'FTM': 20.0,     # Fantom
    'ALGO': 10.0,    # Algorand
    'FLOW': 2.0,     # Flow

    # Default for any other asset
    'DEFAULT': 0.001  # Default minimum for unlisted assets
}

# Kraken withdrawal address keys (must be pre-configured in Kraken account)
KRAKEN_WITHDRAWAL_KEYS = {
    'BTC': 'btc_withdrawal_key',      # Your BTC withdrawal address key
    'ETH': 'eth_withdrawal_key',      # Your ETH withdrawal address key
    'XBT': 'btc_withdrawal_key',      # Bitcoin (Kraken notation)
    'XETH': 'eth_withdrawal_key',     # Ethereum (Kraken notation)
    'USDT': 'usdt_withdrawal_key',    # USDT withdrawal address key
    'USDC': 'usdc_withdrawal_key',    # USDC withdrawal address key
    # Add more as needed
}

# Kraken API settings
KRAKEN_API_KEY = os.getenv('KRAKEN_API_KEY')
KRAKEN_PRIVATE_KEY = os.getenv('KRAKEN_PRIVATE_KEY')
KRAKEN_API_URL = os.getenv('KRAKEN_API_URL', 'https://api.kraken.com')

# Bitcoin settings
BTC_MINIMUM_BALANCE = float(os.getenv('BTC_MINIMUM_BALANCE', '0.0001'))
BTC_API_URL = os.getenv('BTC_API_URL', 'https://blockstream.info/api')
HEALTH_CHECK_INTERVAL = 300  # 5 minutes in seconds

# Price API Configuration
PRICE_API_URL = os.getenv('PRICE_API_URL', 'https://api.coingecko.com/api/v3/simple/price')
