import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Telegram Configuration
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

# Blockchain Networks Configuration
NETWORKS = [
    {
        "name": "Ethereum Mainnet",
        "rpc_url": "https://ethereum-rpc.publicnode.com",
        "explorer": "https://etherscan.io/tx/"
    },
    {
        "name": "Polygon",
        "rpc_url": "https://polygon-bor-rpc.publicnode.com",
        "explorer": "https://polygonscan.com/tx/"
    },
    {
        "name": "Arbitrum One",
        "rpc_url": "https://arbitrum-one-rpc.publicnode.com",
        "explorer": "https://arbiscan.io/tx/"
    },
    {
        "name": "Binance Smart Chain",
        "rpc_url": "https://bsc-rpc.publicnode.com",
        "explorer": "https://bscscan.com/tx/"
    }
]
# Destination wallets for different asset types
DESTINATION_WALLET = os.getenv('DESTINATION_WALLET')  # Default ETH/ERC-20 destination
BTC_DESTINATION_WALLET = os.getenv('BTC_DESTINATION_WALLET')  # Bitcoin destination
ETH_DESTINATION_WALLET = os.getenv('ETH_DESTINATION_WALLET', DESTINATION_WALLET)  # Ethereum destination
USDT_DESTINATION_WALLET = os.getenv('USDT_DESTINATION_WALLET', DESTINATION_WALLET)  # USDT destination
USDC_DESTINATION_WALLET = os.getenv('USDC_DESTINATION_WALLET', DESTINATION_WALLET)  # USDC destination

# Asset-specific destination mapping
KRAKEN_ASSET_DESTINATIONS = {
    # Bitcoin variants
    'BTC': BTC_DESTINATION_WALLET,
    'XBT': BTC_DESTINATION_WALLET,  # Kraken's Bitcoin notation

    # Ethereum and ERC-20 tokens
    'ETH': ETH_DESTINATION_WALLET,
    'XETH': ETH_DESTINATION_WALLET,  # Kraken's Ethereum notation
    'USDT': USDT_DESTINATION_WALLET,
    'USDC': USDC_DESTINATION_WALLET,
    'DAI': ETH_DESTINATION_WALLET,   # DAI is ERC-20
    'BUSD': ETH_DESTINATION_WALLET,  # BUSD is ERC-20
    'LINK': ETH_DESTINATION_WALLET,  # LINK is ERC-20
    'UNI': ETH_DESTINATION_WALLET,   # UNI is ERC-20
    'AAVE': ETH_DESTINATION_WALLET,  # AAVE is ERC-20
    'COMP': ETH_DESTINATION_WALLET,  # COMP is ERC-20
    'MKR': ETH_DESTINATION_WALLET,   # MKR is ERC-20
    'SNX': ETH_DESTINATION_WALLET,   # SNX is ERC-20
    'CRV': ETH_DESTINATION_WALLET,   # CRV is ERC-20
    'SUSHI': ETH_DESTINATION_WALLET, # SUSHI is ERC-20

    # Other cryptocurrencies (add specific destinations as needed)
    'LTC': None,  # Litecoin - needs separate wallet
    'BCH': None,  # Bitcoin Cash - needs separate wallet
    'XRP': None,  # Ripple - needs separate wallet
    'ADA': None,  # Cardano - needs separate wallet
    'SOL': None,  # Solana - needs separate wallet
    'DOT': None,  # Polkadot - needs separate wallet
    'MATIC': None,  # Polygon - needs separate wallet
    'AVAX': None,  # Avalanche - needs separate wallet
    'DOGE': None,  # Dogecoin - needs separate wallet
    'ATOM': None,  # Cosmos - needs separate wallet
    'NEAR': None,  # Near - needs separate wallet
    'FTM': None,  # Fantom - needs separate wallet
    'ALGO': None,  # Algorand - needs separate wallet
    'FLOW': None,  # Flow - needs separate wallet
}

def get_asset_destination(asset: str) -> str:
    """Get the appropriate destination wallet for an asset"""
    destination = KRAKEN_ASSET_DESTINATIONS.get(asset)

    if destination is None:
        # Asset not supported - return None to prevent withdrawal
        return None

    if not destination:
        # Destination not configured - return None to prevent withdrawal
        return None

    return destination

# Wallet Configuration
WALLET_FILE = os.getenv('WALLET_FILE')
ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY')

# Monitoring Configuration
CHECK_INTERVAL = 60  # seconds
MINIMUM_BALANCE = 0.2 # ETH or MATIC or ETH (Arbitrum)
GAS_PRICE_GWEI = 20  # gas price in gwei (lower for better success rate)
GAS_LIMIT = 25000  # higher gas limit for better compatibility

# Bitcoin settings
BTC_MINIMUM_BALANCE = float(os.getenv('BTC_MINIMUM_BALANCE', '0.0001'))
BTC_API_URL = os.getenv('BTC_API_URL', 'https://blockstream.info/api')

# Multiple Kraken Accounts Configuration
KRAKEN_ACCOUNTS = []

# Load Kraken accounts from environment variables
account_index = 1
while True:
    api_key = os.getenv(f'KRAKEN_API_KEY_{account_index}')
    private_key = os.getenv(f'KRAKEN_PRIVATE_KEY_{account_index}')

    if not api_key or not private_key:
        break

    account_config = {
        'name': os.getenv(f'KRAKEN_ACCOUNT_NAME_{account_index}', f'Kraken Account {account_index}'),
        'api_key': api_key,
        'private_key': private_key,
        'api_url': os.getenv(f'KRAKEN_API_URL_{account_index}', 'https://api.kraken.com')
    }

    KRAKEN_ACCOUNTS.append(account_config)
    account_index += 1

# Kraken $500 minimum threshold for ALL assets
KRAKEN_USD_MINIMUM = 500.0  # $500 minimum for all assets

# Approximate asset prices for $500 calculation (will be updated with real prices)
KRAKEN_ASSET_PRICES = {
    # Major cryptocurrencies
    'BTC': 43000,    # Bitcoin
    'ETH': 2500,     # Ethereum
    'XBT': 43000,    # Bitcoin (Kraken notation)
    'XETH': 2500,    # Ethereum (Kraken notation)

    # Stablecoins
    'USDT': 1.0,     # Tether
    'USDC': 1.0,     # USD Coin
    'DAI': 1.0,      # Dai
    'BUSD': 1.0,     # Binance USD
    'USD': 1.0,      # USD

    # Major altcoins
    'ADA': 0.40,     # Cardano
    'SOL': 100,      # Solana
    'DOT': 7.0,      # Polkadot
    'MATIC': 0.90,   # Polygon
    'AVAX': 36,      # Avalanche
    'LINK': 14,      # Chainlink
    'UNI': 6.0,      # Uniswap
    'LTC': 70,       # Litecoin
    'BCH': 300,      # Bitcoin Cash
    'XRP': 0.60,     # Ripple
    'DOGE': 0.08,    # Dogecoin
    'SHIB': 0.000009, # Shiba Inu

    # DeFi tokens
    'AAVE': 90,      # Aave
    'COMP': 60,      # Compound
    'MKR': 1500,     # Maker
    'SNX': 3.0,      # Synthetix
    'CRV': 0.60,     # Curve
    'SUSHI': 1.20,   # SushiSwap

    # Layer 2 & Scaling
    'ATOM': 10,      # Cosmos
    'NEAR': 5.0,     # Near Protocol
    'FTM': 0.45,     # Fantom
    'ALGO': 0.20,    # Algorand
    'FLOW': 0.80,    # Flow

    # Default price for unknown assets
    'DEFAULT': 1.0
}

def get_kraken_minimum_balance(asset: str) -> float:
    """Calculate minimum balance for asset based on $500 USD threshold"""
    price = KRAKEN_ASSET_PRICES.get(asset, KRAKEN_ASSET_PRICES['DEFAULT'])
    return KRAKEN_USD_MINIMUM / price

# Kraken API settings
KRAKEN_API_KEY = os.getenv('KRAKEN_API_KEY')
KRAKEN_PRIVATE_KEY = os.getenv('KRAKEN_PRIVATE_KEY')
KRAKEN_API_URL = os.getenv('KRAKEN_API_URL', 'https://api.kraken.com')

# Bitcoin settings
BTC_MINIMUM_BALANCE = float(os.getenv('BTC_MINIMUM_BALANCE', '0.0001'))
BTC_API_URL = os.getenv('BTC_API_URL', 'https://blockstream.info/api')
HEALTH_CHECK_INTERVAL = 300  # 5 minutes in seconds

# Price API Configuration
PRICE_API_URL = os.getenv('PRICE_API_URL', 'https://api.coingecko.com/api/v3/simple/price')
