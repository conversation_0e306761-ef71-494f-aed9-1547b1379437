version: '3.8'

services:
  predator-wallet-monitor:
    build: .
    container_name: predator-wallet-monitor
    restart: unless-stopped
    environment:
      - PYTHONUNBUFFERED=1
    env_file:
      - .env
    volumes:
      # Mount logs directory for persistent logging
      - ./logs:/app/logs
      # Mount wallet file for persistent storage
      - ./wallets.enc:/app/wallets.enc
      # Mount main.log for persistent logging
      - ./main.log:/app/main.log
    networks:
      - predator-network
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; import os; requests.get(f'https://api.telegram.org/bot{os.getenv(\"TELEGRAM_BOT_TOKEN\")}/getMe', timeout=10)"]
      interval: 5m
      timeout: 30s
      retries: 3
      start_period: 1m
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  predator-network:
    driver: bridge
