import asyncio
import logging
import time
from datetime import datetime
from telegram.ext import <PERSON><PERSON><PERSON>er, CommandHandler, ContextT<PERSON>s, MessageHandler, filters
from telegram import Update
from utils import (
    init_web3,
    load_wallets,
    add_wallet,
    format_eth_amount,
    send_telegram_message,
    derive_private_key_from_mnemonic
)
from config import TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, NETWORKS, DESTINATION_WALLET, CHECK_INTERVAL, MINIMUM_BALANCE, GAS_PRICE_GWEI, GAS_LIMIT, HEALTH_CHECK_INTERVAL

# Global variables for monitoring
last_heartbeat = time.time()
watcher_status = "Starting..."
notified_balances = {}  # Track which wallets we've already notified about
last_health_check = time.time()
bot_down_notified = False

# Set up logging to file and console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    handlers=[
        logging.FileHandler("main.log"),
        logging.StreamHandler()
    ]
)

async def log_and_reply(update, message, level="info"):
    user = update.effective_user
    log_msg = f"[{user.id} - {user.username}] {message}"
    if level == "error":
        logging.error(log_msg)
    else:
        logging.info(log_msg)
    await update.message.reply_text(message)

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    welcome_msg = (
        "🦁 PREDATOR WALLET MONITOR 🦁\n"
        "═══════════════════════════════════\n\n"
        "🚀 ACTIVE MONITORING SYSTEM\n"
        "💰 Auto-withdrawal when balance detected\n"
        "🔔 Real-time notifications with USD values\n"
        "🛡️ Multi-network support (ETH, Polygon, Arbitrum, BSC)\n\n"
        "📋 AVAILABLE COMMANDS:\n"
        "┌─────────────────────────────────────┐\n"
        "│ 📊 /status    - Check wallet balances │\n"
        "│ 👛 /wallets   - List all wallets     │\n"
        "│ 🏦 /kraken    - Check Kraken balances │\n"
        "│ ➕ /addwallet - Add private key      │\n"
        "│ 🌱 /addmnemonic - Add seed phrase    │\n"
        "│ 💸 /withdraw  - Manual withdrawal    │\n"
        "│ 🏥 /health    - System health check  │\n"
        "│ 💓 /ping      - Bot status & heartbeat│\n"
        "│ ❓ /help      - Show this menu       │\n"
        "└─────────────────────────────────────┘\n\n"
        "🔒 SECURITY: All commands require authorization\n"
        "⚡ STATUS: Monitoring active across all networks\n"
        "🎯 READY: Your crypto predator is hunting!"
    )
    await log_and_reply(update, welcome_msg)

async def ping(update: Update, context: ContextTypes.DEFAULT_TYPE):
    global last_heartbeat, watcher_status
    current_time = time.time()
    time_since_heartbeat = current_time - last_heartbeat
    
    if time_since_heartbeat > CHECK_INTERVAL * 2:
        status_emoji = "❌"
        watcher_msg = f"OFFLINE (last seen {int(time_since_heartbeat)}s ago)"
    else:
        status_emoji = "✅"
        watcher_msg = f"ONLINE ({watcher_status})"
    
    ping_msg = (
        f"💓 BOT STATUS REPORT 💓\n"
        f"═══════════════════════════\n\n"
        f"{status_emoji} Bot Status: ALIVE\n"
        f"🔍 Watcher: {watcher_msg}\n"
        f"⏰ Current Time: {datetime.fromtimestamp(current_time).strftime('%Y-%m-%d %H:%M:%S')}\n"
        f"🔄 Check Interval: {CHECK_INTERVAL}s\n"
        f"💰 Min Balance: {MINIMUM_BALANCE} ETH\n"
        f"🌐 Networks: {len(NETWORKS)} active"
    )
    await log_and_reply(update, ping_msg)

async def kraken_status(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Get Kraken account status and balances"""
    try:
        from kraken_watcher import kraken_watcher
        status_msg = kraken_watcher.get_balance_summary()
        await log_and_reply(update, status_msg)
    except ImportError as e:
        error_msg = f"❌ Kraken integration not available: {str(e)}"
        await log_and_reply(update, error_msg, level="error")
    except Exception as e:
        error_msg = f"❌ Error getting Kraken status: {str(e)}"
        await log_and_reply(update, error_msg, level="error")

async def wallets_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    wallets = load_wallets()
    if not wallets:
        error_msg = (
            "❌ NO WALLETS FOUND\n"
            "═══════════════════════\n\n"
            "🔧 Use /addwallet or /addmnemonic to add wallets"
        )
        await log_and_reply(update, error_msg, level="error")
        return
    
    wallet_msg = (
        f"👛 LOADED WALLETS 👛\n"
        f"═══════════════════════\n\n"
        f"📊 Total: {len(wallets)} wallets\n\n"
    )
    
    for i, w in enumerate(wallets, 1):
        wallet_msg += (
            f"{i}. {w['name'] or 'Unnamed'}\n"
            f"📍 {w['address']}\n\n"
        )
    
    await log_and_reply(update, wallet_msg)

async def status(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logging.info(f"/status called by {update.effective_user.id}")
    if str(update.message.chat_id) != TELEGRAM_CHAT_ID:
        await log_and_reply(update, "❌ UNAUTHORIZED ACCESS", level="error")
        return
    
    wallets = load_wallets()
    if not wallets:
        await log_and_reply(update, "❌ NO WALLETS CONFIGURED", level="error")
        return
    
    status_msg = (
        f"📊 WALLET STATUS REPORT 📊\n"
        f"═══════════════════════════════\n\n"
        f"🔍 Scanning {len(wallets)} wallets across {len(NETWORKS)} networks\n\n"
    )
    
    for wallet in wallets:
        status_msg += f"👛 {wallet['name'] or 'Unnamed'}\n"
        status_msg += f"📍 {wallet['address']}\n"
        
        for network in NETWORKS:
            try:
                web3 = init_web3(network["rpc_url"])
                balance = web3.eth.get_balance(wallet['address'])
                balance_str = format_eth_amount(balance, network['name'])
                status_msg += f"🌐 {network['name']}: {balance_str}\n"
            except Exception as e:
                status_msg += f"❌ {network['name']}: Error\n"
                logging.error(f"Error checking {network['name']}: {e}")
        status_msg += "\n"
    
    await log_and_reply(update, status_msg)

async def add_wallet_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logging.info(f"/addwallet called by {update.effective_user.id}")
    if str(update.message.chat_id) != TELEGRAM_CHAT_ID:
        await log_and_reply(update, "❌ **UNAUTHORIZED ACCESS**", level="error")
        return
    if len(context.args) < 1:
        usage_msg = (
            "📝 **ADD WALLET USAGE** 📝\n"
            "═══════════════════════════\n\n"
            "**Format:** `/addwallet <private_key> [name]`\n\n"
            "**Example:**\n"
            "`/addwallet 0x123...abc MyWallet`"
        )
        await log_and_reply(update, usage_msg, level="error")
        return

    private_key = context.args[0]
    name = " ".join(context.args[1:]) if len(context.args) > 1 else ""

    try:
        # Derive address from private key automatically
        from web3 import Web3
        account = Web3().eth.account.from_key(private_key)
        address = account.address

        if add_wallet(private_key, name):
            success_msg = (
                f"✅ **WALLET ADDED SUCCESSFULLY** ✅\n"
                f"═══════════════════════════════════\n\n"
                f"👛 **Name:** {name or 'Unnamed'}\n"
                f"📍 **Address:** `{address}`\n"
                f"🔐 **Security:** Private key encrypted\n"
                f"🎯 **Status:** Now monitoring across all networks"
            )
            await log_and_reply(update, success_msg)
        else:
            await log_and_reply(update, "❌ **FAILED TO ADD WALLET**", level="error")
    except Exception as e:
        error_msg = (
            f"❌ **WALLET ADD ERROR** ❌\n"
            f"═══════════════════════════\n\n"
            f"🚫 **Error:** {str(e)}\n"
            f"💡 **Tip:** Check private key format"
        )
        await log_and_reply(update, error_msg, level="error")

async def add_mnemonic_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logging.info(f"/addmnemonic called by {update.effective_user.id}")
    if str(update.message.chat_id) != TELEGRAM_CHAT_ID:
        await log_and_reply(update, "❌ **UNAUTHORIZED ACCESS**", level="error")
        return
    if len(context.args) < 12:
        usage_msg = (
            "📝 **ADD MNEMONIC USAGE** 📝\n"
            "═══════════════════════════════\n\n"
            "**Format:** `/addmnemonic <12-word-seed> [name]`\n\n"
            "**Example:**\n"
            "`/addmnemonic word1 word2 ... word12 MyWallet`"
        )
        await log_and_reply(update, usage_msg, level="error")
        return

    mnemonic = " ".join(context.args[:12])
    name = " ".join(context.args[12:]) if len(context.args) > 12 else ""

    try:
        priv, addr = derive_private_key_from_mnemonic(mnemonic)
        if not priv or not addr:
            await log_and_reply(update, "❌ **INVALID MNEMONIC PHRASE**", level="error")
            return

        from utils import save_wallets, load_wallets
        wallets = load_wallets()
        wallets.append({"address": addr, "private_key": priv, "name": name})

        if save_wallets(wallets):
            success_msg = (
                f"✅ **MNEMONIC WALLET ADDED** ✅\n"
                f"═══════════════════════════════\n\n"
                f"👛 **Name:** {name or 'Unnamed'}\n"
                f"📍 **Address:** `{addr}`\n"
                f"🌱 **Source:** 12-word seed phrase\n"
                f"🔐 **Security:** Encrypted storage\n"
                f"🎯 **Status:** Now monitoring across all networks"
            )
            await log_and_reply(update, success_msg)
        else:
            await log_and_reply(update, "❌ **FAILED TO SAVE WALLET**", level="error")
    except Exception as e:
        error_msg = (
            f"❌ **MNEMONIC ADD ERROR** ❌\n"
            f"═══════════════════════════════\n\n"
            f"🚫 **Error:** {str(e)}\n"
            f"💡 **Tip:** Check mnemonic phrase format"
        )
        await log_and_reply(update, error_msg, level="error")

# Health monitoring function
async def health_monitor():
    global last_health_check, bot_down_notified
    
    while True:
        try:
            current_time = time.time()
            time_since_last_check = current_time - last_health_check
            
            # Check if bot has been down for more than health check interval
            if time_since_last_check > HEALTH_CHECK_INTERVAL:
                if not bot_down_notified:
                    down_msg = (
                        "🚨 **CRITICAL ALERT** 🚨\n"
                        "═══════════════════════\n\n"
                        "❌ **BOT STATUS:** DOWN\n"
                        f"⏰ **Last Check:** {datetime.fromtimestamp(last_health_check).strftime('%Y-%m-%d %H:%M:%S')}\n"
                        f"🕐 **Down Time:** {int(time_since_last_check/60)} minutes\n\n"
                        "🔧 **ACTION REQUIRED:** Check bot immediately!"
                    )
                    send_telegram_message(down_msg)
                    bot_down_notified = True
            else:
                # Reset notification flag if bot is back up
                if bot_down_notified:
                    up_msg = (
                        "✅ **RECOVERY ALERT** ✅\n"
                        "═══════════════════════\n\n"
                        "🟢 **BOT STATUS:** BACK ONLINE\n"
                        f"⏰ **Recovery Time:** {datetime.fromtimestamp(current_time).strftime('%Y-%m-%d %H:%M:%S')}\n"
                        "🎯 **Monitoring resumed successfully!**"
                    )
                    send_telegram_message(up_msg)
                    bot_down_notified = False
                
                last_health_check = current_time
            
            await asyncio.sleep(60)  # Check every minute

        except Exception as e:
            logging.error(f"Health monitor error: {e}")
            await asyncio.sleep(60)

async def kraken_monitor_loop():
    """Main Kraken monitoring loop"""
    try:
        from kraken_watcher import kraken_watcher
    except ImportError as e:
        logging.error(f"Failed to import kraken_watcher: {e}")
        return

    while True:
        try:
            await kraken_watcher.check_all_balances()
            await asyncio.sleep(60)  # Check every minute

        except Exception as e:
            logging.error(f"Kraken monitor error: {e}")
            await asyncio.sleep(60)  # Wait 1 minute before retry

def main():
    # Create event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    # Start Telegram bot
    app = ApplicationBuilder().token(TELEGRAM_BOT_TOKEN).build()
    app.add_handler(CommandHandler("start", start))
    app.add_handler(CommandHandler("help", start))
    app.add_handler(CommandHandler("status", status))
    app.add_handler(CommandHandler("wallets", wallets_cmd))
    app.add_handler(CommandHandler("addwallet", add_wallet_command))
    app.add_handler(CommandHandler("addmnemonic", add_mnemonic_command))
    app.add_handler(CommandHandler("ping", ping))
    app.add_handler(CommandHandler("kraken", kraken_status))
    
    logging.info("🚀 Starting Enhanced Predator Wallet Monitor...")

    # Import and start the enhanced watcher
    from watcher_enhanced import enhanced_watcher_loop

    # Start background tasks
    loop = asyncio.get_event_loop()
    loop.create_task(health_monitor())
    loop.create_task(enhanced_watcher_loop())
    loop.create_task(kraken_monitor_loop())

    # Run the bot
    app.run_polling()

if __name__ == "__main__":
    import sys
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    main()
