@echo off

if "%1"=="start" goto start
if "%1"=="stop" goto stop
if "%1"=="restart" goto restart
if "%1"=="logs" goto logs
if "%1"=="status" goto status
if "%1"=="shell" goto shell
if "%1"=="build" goto build
if "%1"=="update" goto update
if "%1"=="clean" goto clean
goto help

:start
echo 🚀 Starting Predator Wallet Monitor...
docker-compose up -d
goto end

:stop
echo 🛑 Stopping Predator Wallet Monitor...
docker-compose down
goto end

:restart
echo 🔄 Restarting Predator Wallet Monitor...
docker-compose restart
goto end

:logs
echo 📊 Showing logs (Ctrl+C to exit)...
docker-compose logs -f
goto end

:status
echo 📋 Container status:
docker-compose ps
echo.
echo 🏥 Health status:
docker inspect predator-wallet-monitor --format="{{.State.Health.Status}}" 2>nul || echo Health check not available
goto end

:shell
echo 🐚 Opening shell in container...
docker-compose exec predator-wallet-monitor bash
goto end

:build
echo 🔧 Rebuilding container...
docker-compose build --no-cache
goto end

:update
echo 🔄 Updating and rebuilding...
docker-compose down
docker-compose build --no-cache
docker-compose up -d
goto end

:clean
echo 🧹 Cleaning up...
docker-compose down
docker system prune -f
goto end

:help
echo 🦁 Predator Wallet Monitor - Docker Management
echo.
echo Usage: %0 {start^|stop^|restart^|logs^|status^|shell^|build^|update^|clean}
echo.
echo Commands:
echo   start   - Start the container
echo   stop    - Stop the container
echo   restart - Restart the container
echo   logs    - View live logs
echo   status  - Show container status
echo   shell   - Open shell in container
echo   build   - Rebuild the container
echo   update  - Stop, rebuild, and start
echo   clean   - Stop and clean up Docker resources
echo.

:end
if not "%1"=="logs" pause
