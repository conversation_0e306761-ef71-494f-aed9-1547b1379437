import hashlib
import hmac
import base64
import time
import urllib.parse
import requests
import logging
from typing import Dict, Optional, Any

class KrakenAPIClient:
    """
    Kraken API client for balance monitoring and withdrawals
    Supports multiple accounts
    """

    def __init__(self, account_config: Dict[str, Any]):
        self.account_name = account_config.get('name', 'Unknown Account')
        self.api_key = account_config.get('api_key')
        self.private_key = account_config.get('private_key')
        self.api_url = account_config.get('api_url', 'https://api.kraken.com')
        self.session = requests.Session()
        
    def _get_kraken_signature(self, urlpath: str, data: Dict[str, Any], secret: str) -> str:
        """Generate Kraken API signature"""
        postdata = urllib.parse.urlencode(data)
        encoded = (str(data['nonce']) + postdata).encode()
        message = urlpath.encode() + hashlib.sha256(encoded).digest()
        
        mac = hmac.new(base64.b64decode(secret), message, hashlib.sha512)
        sigdigest = base64.b64encode(mac.digest())
        return sigdigest.decode()
    
    def _kraken_request(self, uri_path: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Make authenticated request to Kraken API"""
        if not self.api_key or not self.private_key:
            logging.error("Kraken API credentials not configured")
            return None
            
        try:
            data['nonce'] = str(int(1000 * time.time()))
            
            headers = {
                'API-Key': self.api_key,
                'API-Sign': self._get_kraken_signature(uri_path, data, self.private_key),
                'User-Agent': 'PredatorWalletMonitor/1.0'
            }
            
            response = self.session.post(
                f"{self.api_url}{uri_path}",
                headers=headers,
                data=data,
                timeout=30
            )
            
            response.raise_for_status()
            result = response.json()
            
            if result.get('error'):
                logging.error(f"Kraken API error: {result['error']}")
                return None
                
            return result.get('result')
            
        except requests.exceptions.RequestException as e:
            logging.error(f"Kraken API request failed: {e}")
            return None
        except Exception as e:
            logging.error(f"Kraken API error: {e}")
            return None
    
    def get_account_balance(self) -> Optional[Dict[str, float]]:
        """Get account balances from Kraken"""
        try:
            result = self._kraken_request('/0/private/Balance', {})
            if not result:
                return None
                
            # Convert string balances to float and filter out zero balances
            balances = {}
            for asset, balance_str in result.items():
                balance = float(balance_str)
                if balance > 0:
                    balances[asset] = balance
                    
            return balances
            
        except Exception as e:
            logging.error(f"Failed to get Kraken balances: {e}")
            return None
    
    def get_withdrawal_methods(self, asset: str) -> Optional[Dict[str, Any]]:
        """Get available withdrawal methods for an asset"""
        try:
            result = self._kraken_request('/0/private/WithdrawMethods', {'asset': asset})
            return result
        except Exception as e:
            logging.error(f"Failed to get withdrawal methods for {asset}: {e}")
            return None
    
    def withdraw_funds(self, asset: str, address_or_key: str, amount: float) -> Optional[Dict[str, Any]]:
        """
        Withdraw funds from Kraken

        Args:
            asset: Asset to withdraw (e.g., 'BTC', 'ETH')
            address_or_key: Withdrawal address or pre-configured key
            amount: Amount to withdraw
        """
        try:
            # First, try to find if this address is already configured as a withdrawal key
            withdrawal_key = self._find_or_create_withdrawal_key(asset, address_or_key)
            if not withdrawal_key:
                logging.error(f"Could not configure withdrawal address for {asset}: {address_or_key}")
                return None

            data = {
                'asset': asset,
                'key': withdrawal_key,
                'amount': str(amount)
            }

            result = self._kraken_request('/0/private/Withdraw', data)
            if result:
                logging.info(f"Kraken withdrawal initiated: {amount} {asset} to {address_or_key}")
                return result
            else:
                logging.error(f"Failed to initiate Kraken withdrawal: {amount} {asset}")
                return None

        except Exception as e:
            logging.error(f"Kraken withdrawal error: {e}")
            return None

    def _find_or_create_withdrawal_key(self, asset: str, address: str) -> Optional[str]:
        """Find existing withdrawal key for address or suggest manual configuration"""
        try:
            # Get existing withdrawal addresses
            addresses = self.get_withdrawal_addresses(asset)
            if addresses:
                # Look for matching address
                for key, addr_info in addresses.items():
                    if isinstance(addr_info, dict) and addr_info.get('address') == address:
                        logging.info(f"Found existing withdrawal key '{key}' for {asset} address {address}")
                        return key

            # Address not found - return a suggested key name for manual setup
            suggested_key = f"auto_{asset.lower()}_wallet"
            logging.warning(f"Address {address} not configured in Kraken for {asset}. Please add it manually with key: {suggested_key}")
            return None

        except Exception as e:
            logging.error(f"Error finding withdrawal key for {asset}: {e}")
            return None
    
    def get_withdrawal_addresses(self, asset: str) -> Optional[Dict[str, Any]]:
        """Get configured withdrawal addresses for an asset"""
        try:
            result = self._kraken_request('/0/private/WithdrawAddresses', {'asset': asset})
            return result
        except Exception as e:
            logging.error(f"Failed to get withdrawal addresses for {asset}: {e}")
            return None
    
    def get_withdrawal_info(self, asset: str, address_or_key: str, amount: float) -> Optional[Dict[str, Any]]:
        """Get withdrawal information including fees"""
        try:
            # Find or suggest withdrawal key for the address
            withdrawal_key = self._find_or_create_withdrawal_key(asset, address_or_key)
            if not withdrawal_key:
                # Return estimated fee info if we can't find the key
                return self._get_estimated_withdrawal_info(asset, amount)

            data = {
                'asset': asset,
                'key': withdrawal_key,
                'amount': str(amount)
            }

            result = self._kraken_request('/0/private/WithdrawInfo', data)
            return result
        except Exception as e:
            logging.error(f"Failed to get withdrawal info for {asset}: {e}")
            return self._get_estimated_withdrawal_info(asset, amount)

    def _get_estimated_withdrawal_info(self, asset: str, amount: float) -> Dict[str, Any]:
        """Get estimated withdrawal info when exact info is unavailable"""
        # Estimated fees for common assets
        estimated_fees = {
            'BTC': 0.0005,
            'XBT': 0.0005,
            'ETH': 0.005,
            'XETH': 0.005,
            'USDT': 5.0,
            'USDC': 5.0,
            'LTC': 0.001,
            'BCH': 0.001,
            'XRP': 0.02,
            'ADA': 1.0,
            'SOL': 0.01,
            'DOT': 0.1,
            'MATIC': 1.0,
            'AVAX': 0.01,
            'DOGE': 2.0,
        }

        fee = estimated_fees.get(asset, 0.01)  # Default 0.01 for unknown assets

        return {
            'fee': fee,
            'amount': amount - fee,
            'estimated': True
        }

def format_kraken_balance(balance: float, asset: str) -> str:
    """Format Kraken balance for display"""
    if asset == 'BTC':
        return f"{balance:.8f} BTC"
    elif asset in ['ETH']:
        return f"{balance:.6f} {asset}"
    elif asset in ['USDT', 'USDC', 'USD']:
        return f"{balance:.2f} {asset}"
    else:
        return f"{balance:.4f} {asset}"

def get_usd_value(amount: float, asset: str) -> Optional[float]:
    """Get USD value of asset amount"""
    try:
        # Simple price mapping - in production, use real price API
        price_map = {
            'BTC': 43000,  # Approximate BTC price
            'ETH': 2500,   # Approximate ETH price
            'USDT': 1.0,
            'USDC': 1.0,
            'USD': 1.0
        }
        
        if asset in price_map:
            return amount * price_map[asset]
        return None
        
    except Exception:
        return None
