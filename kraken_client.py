import hashlib
import hmac
import base64
import time
import urllib.parse
import requests
import logging
from typing import Dict, Optional, Any
from config import KRAKEN_API_KEY, KRAKEN_PRIVATE_KEY, KRAKEN_API_URL

class KrakenAPIClient:
    """
    Kraken API client for balance monitoring and withdrawals
    """
    
    def __init__(self):
        self.api_key = KRAKEN_API_KEY
        self.private_key = KRAKEN_PRIVATE_KEY
        self.api_url = KRAKEN_API_URL
        self.session = requests.Session()
        
    def _get_kraken_signature(self, urlpath: str, data: Dict[str, Any], secret: str) -> str:
        """Generate Kraken API signature"""
        postdata = urllib.parse.urlencode(data)
        encoded = (str(data['nonce']) + postdata).encode()
        message = urlpath.encode() + hashlib.sha256(encoded).digest()
        
        mac = hmac.new(base64.b64decode(secret), message, hashlib.sha512)
        sigdigest = base64.b64encode(mac.digest())
        return sigdigest.decode()
    
    def _kraken_request(self, uri_path: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Make authenticated request to Kraken API"""
        if not self.api_key or not self.private_key:
            logging.error("Kraken API credentials not configured")
            return None
            
        try:
            data['nonce'] = str(int(1000 * time.time()))
            
            headers = {
                'API-Key': self.api_key,
                'API-Sign': self._get_kraken_signature(uri_path, data, self.private_key),
                'User-Agent': 'PredatorWalletMonitor/1.0'
            }
            
            response = self.session.post(
                f"{self.api_url}{uri_path}",
                headers=headers,
                data=data,
                timeout=30
            )
            
            response.raise_for_status()
            result = response.json()
            
            if result.get('error'):
                logging.error(f"Kraken API error: {result['error']}")
                return None
                
            return result.get('result')
            
        except requests.exceptions.RequestException as e:
            logging.error(f"Kraken API request failed: {e}")
            return None
        except Exception as e:
            logging.error(f"Kraken API error: {e}")
            return None
    
    def get_account_balance(self) -> Optional[Dict[str, float]]:
        """Get account balances from Kraken"""
        try:
            result = self._kraken_request('/0/private/Balance', {})
            if not result:
                return None
                
            # Convert string balances to float and filter out zero balances
            balances = {}
            for asset, balance_str in result.items():
                balance = float(balance_str)
                if balance > 0:
                    balances[asset] = balance
                    
            return balances
            
        except Exception as e:
            logging.error(f"Failed to get Kraken balances: {e}")
            return None
    
    def get_withdrawal_methods(self, asset: str) -> Optional[Dict[str, Any]]:
        """Get available withdrawal methods for an asset"""
        try:
            result = self._kraken_request('/0/private/WithdrawMethods', {'asset': asset})
            return result
        except Exception as e:
            logging.error(f"Failed to get withdrawal methods for {asset}: {e}")
            return None
    
    def withdraw_funds(self, asset: str, key: str, amount: float) -> Optional[Dict[str, Any]]:
        """
        Withdraw funds from Kraken
        
        Args:
            asset: Asset to withdraw (e.g., 'BTC', 'ETH')
            key: Withdrawal address key (must be pre-configured in Kraken)
            amount: Amount to withdraw
        """
        try:
            data = {
                'asset': asset,
                'key': key,
                'amount': str(amount)
            }
            
            result = self._kraken_request('/0/private/Withdraw', data)
            if result:
                logging.info(f"Kraken withdrawal initiated: {amount} {asset} to {key}")
                return result
            else:
                logging.error(f"Failed to initiate Kraken withdrawal: {amount} {asset}")
                return None
                
        except Exception as e:
            logging.error(f"Kraken withdrawal error: {e}")
            return None
    
    def get_withdrawal_addresses(self, asset: str) -> Optional[Dict[str, Any]]:
        """Get configured withdrawal addresses for an asset"""
        try:
            result = self._kraken_request('/0/private/WithdrawAddresses', {'asset': asset})
            return result
        except Exception as e:
            logging.error(f"Failed to get withdrawal addresses for {asset}: {e}")
            return None
    
    def get_withdrawal_info(self, asset: str, key: str, amount: float) -> Optional[Dict[str, Any]]:
        """Get withdrawal information including fees"""
        try:
            data = {
                'asset': asset,
                'key': key,
                'amount': str(amount)
            }
            
            result = self._kraken_request('/0/private/WithdrawInfo', data)
            return result
        except Exception as e:
            logging.error(f"Failed to get withdrawal info for {asset}: {e}")
            return None

def format_kraken_balance(balance: float, asset: str) -> str:
    """Format Kraken balance for display"""
    if asset == 'BTC':
        return f"{balance:.8f} BTC"
    elif asset in ['ETH']:
        return f"{balance:.6f} {asset}"
    elif asset in ['USDT', 'USDC', 'USD']:
        return f"{balance:.2f} {asset}"
    else:
        return f"{balance:.4f} {asset}"

def get_usd_value(amount: float, asset: str) -> Optional[float]:
    """Get USD value of asset amount"""
    try:
        # Simple price mapping - in production, use real price API
        price_map = {
            'BTC': 43000,  # Approximate BTC price
            'ETH': 2500,   # Approximate ETH price
            'USDT': 1.0,
            'USDC': 1.0,
            'USD': 1.0
        }
        
        if asset in price_map:
            return amount * price_map[asset]
        return None
        
    except Exception:
        return None
