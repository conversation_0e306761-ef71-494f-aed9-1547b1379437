import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, Optional, Set
from kraken_client import KrakenAPIClient, format_kraken_balance, get_usd_value
from config import KRAKEN_ACCOUNTS, KRAKEN_USD_MINIMUM, BTC_DESTINATION_WALLET, get_kraken_minimum_balance
from utils import send_telegram_message

class KrakenWatcher:
    """
    Enhanced Kraken watcher that monitors ALL balances and auto-withdraws
    """
    
    def __init__(self):
        self.clients = []
        self.last_balances = {}
        self.notified_balances = set()  # Track which balances we've already notified about
        self.withdrawal_cooldown = {}   # Track withdrawal cooldowns per asset per account

        # Initialize clients for each Kraken account
        for account_config in KRAKEN_ACCOUNTS:
            try:
                client = KrakenAPIClient(account_config)
                self.clients.append(client)
                logging.info(f"Initialized Kraken client for: {client.account_name}")
            except Exception as e:
                logging.error(f"Failed to initialize Kraken client for {account_config.get('name', 'Unknown')}: {e}")

        if not self.clients:
            logging.warning("No Kraken accounts configured. Add KRAKEN_API_KEY_1, KRAKEN_PRIVATE_KEY_1, etc. to .env file")
        
    async def check_all_balances(self):
        """Check all Kraken balances across multiple accounts and trigger withdrawals if needed"""
        if not self.clients:
            return

        for client in self.clients:
            try:
                await self.check_account_balances(client)
            except Exception as e:
                logging.error(f"Error checking balances for {client.account_name}: {e}")

    async def check_account_balances(self, client: KrakenAPIClient):
        """Check balances for a specific Kraken account"""
        try:
            balances = client.get_account_balance()
            if not balances:
                logging.warning(f"Failed to get balances for {client.account_name}")
                return

            # Process each balance
            for asset, balance in balances.items():
                await self.process_asset_balance(client, asset, balance)

            # Update last known balances for this account
            account_key = f"{client.account_name}_{client.api_key[-8:]}"
            self.last_balances[account_key] = balances.copy()

        except Exception as e:
            logging.error(f"Error checking balances for {client.account_name}: {e}")

    async def process_asset_balance(self, client: KrakenAPIClient, asset: str, balance: float):
        """Process individual asset balance with $500 minimum threshold"""
        try:
            # Calculate minimum balance based on $500 USD threshold
            min_balance = get_kraken_minimum_balance(asset)

            # Get USD value of current balance
            usd_value = get_usd_value(balance, asset)

            # Check if balance exceeds $500 minimum
            if usd_value and usd_value >= KRAKEN_USD_MINIMUM:
                await self.handle_balance_detected(client, asset, balance, usd_value)
            else:
                # Remove from notified set if balance dropped below threshold
                balance_key = f"{client.account_name}_{asset}"
                self.notified_balances.discard(balance_key)

        except Exception as e:
            logging.error(f"Error processing {asset} balance for {client.account_name}: {e}")

    async def handle_balance_detected(self, client: KrakenAPIClient, asset: str, balance: float, usd_value: float):
        """Handle when a balance exceeds $500 minimum threshold"""
        try:
            balance_key = f"{client.account_name}_{asset}_{balance:.8f}"

            # Check if we've already notified about this balance
            if balance_key not in self.notified_balances:
                await self.send_balance_notification(client, asset, balance, usd_value)
                self.notified_balances.add(balance_key)

            # Check withdrawal cooldown
            cooldown_key = f"{client.account_name}_{asset}"
            if self.is_withdrawal_on_cooldown(cooldown_key):
                return

            # Attempt automatic withdrawal
            await self.attempt_withdrawal(client, asset, balance, usd_value)

        except Exception as e:
            logging.error(f"Error handling {asset} balance for {client.account_name}: {e}")
    
    async def send_balance_notification(self, asset: str, balance: float):
        """Send balance detection notification"""
        try:
            balance_formatted = format_kraken_balance(balance, asset)
            usd_value = get_usd_value(balance, asset)
            usd_text = f" (${usd_value:,.2f})" if usd_value else ""
            
            notification_msg = (
                f"💰 KRAKEN BALANCE DETECTED 💰\n"
                f"═══════════════════════════════\n\n"
                f"🏦 Exchange: Kraken\n"
                f"💎 Asset: {asset}\n"
                f"💰 Balance: {balance_formatted}{usd_text}\n"
                f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                f"🎯 Status: Checking withdrawal options..."
            )
            
            send_telegram_message(notification_msg)
            logging.info(f"Kraken balance detected: {balance_formatted} {asset}")
            
        except Exception as e:
            logging.error(f"Error sending balance notification: {e}")
    
    async def attempt_withdrawal(self, client: KrakenAPIClient, asset: str, balance: float, usd_value: float):
        """Attempt to withdraw the asset"""
        try:
            # Check if we have a withdrawal key configured for this account
            withdrawal_key = client.withdrawal_keys.get(asset)
            if not withdrawal_key:
                await self.send_no_withdrawal_key_notification(client, asset, balance, usd_value)
                return

            # Get withdrawal info to check fees
            withdrawal_info = client.get_withdrawal_info(asset, withdrawal_key, balance)
            if not withdrawal_info:
                await self.send_withdrawal_error_notification(client, asset, balance, usd_value, "Failed to get withdrawal info")
                return

            # Calculate amount after fees
            fee = float(withdrawal_info.get('fee', 0))
            amount_to_withdraw = balance - fee

            if amount_to_withdraw <= 0:
                await self.send_insufficient_for_fee_notification(client, asset, balance, fee, usd_value)
                return

            # Perform withdrawal
            result = client.withdraw_funds(asset, withdrawal_key, amount_to_withdraw)

            if result:
                await self.send_withdrawal_success_notification(client, asset, amount_to_withdraw, fee, usd_value, result)
                cooldown_key = f"{client.account_name}_{asset}"
                self.set_withdrawal_cooldown(cooldown_key)
            else:
                await self.send_withdrawal_error_notification(client, asset, balance, usd_value, "Withdrawal request failed")

        except Exception as e:
            logging.error(f"Error attempting withdrawal for {asset} on {client.account_name}: {e}")
            await self.send_withdrawal_error_notification(client, asset, balance, usd_value, str(e))
    
    async def send_withdrawal_success_notification(self, asset: str, amount: float, fee: float, result: Dict):
        """Send successful withdrawal notification"""
        try:
            amount_formatted = format_kraken_balance(amount, asset)
            fee_formatted = format_kraken_balance(fee, asset)
            usd_value = get_usd_value(amount, asset)
            usd_text = f" (${usd_value:,.2f})" if usd_value else ""
            
            # Get destination based on asset
            if asset in ['BTC', 'XBT']:
                destination = BTC_DESTINATION_WALLET
            else:
                destination = "Configured withdrawal address"
            
            success_msg = (
                f"🚨 KRAKEN WITHDRAWAL SUCCESSFUL 🚨\n"
                f"═══════════════════════════════════════\n\n"
                f"⚡ AUTO-WITHDRAWAL EXECUTED\n\n"
                f"🏦 Exchange: Kraken\n"
                f"💎 Asset: {asset}\n"
                f"💰 Amount: {amount_formatted}{usd_text}\n"
                f"💸 Fee: {fee_formatted}\n"
                f"🎯 To: {destination}\n"
                f"🔗 Ref ID: {result.get('refid', 'N/A')}\n"
                f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                f"✅ Status: Funds secured successfully!"
            )
            
            send_telegram_message(success_msg)
            logging.info(f"Kraken withdrawal successful: {amount_formatted} {asset}")
            
        except Exception as e:
            logging.error(f"Error sending withdrawal success notification: {e}")
    
    async def send_withdrawal_error_notification(self, asset: str, balance: float, error: str):
        """Send withdrawal error notification"""
        try:
            balance_formatted = format_kraken_balance(balance, asset)
            
            error_msg = (
                f"❌ KRAKEN WITHDRAWAL ERROR ❌\n"
                f"═══════════════════════════════════\n\n"
                f"🏦 Exchange: Kraken\n"
                f"💎 Asset: {asset}\n"
                f"💰 Balance: {balance_formatted}\n"
                f"🚫 Error: {error}\n"
                f"🔄 Status: Will retry on next check"
            )
            
            send_telegram_message(error_msg)
            
        except Exception as e:
            logging.error(f"Error sending withdrawal error notification: {e}")
    
    async def send_no_withdrawal_key_notification(self, asset: str, balance: float):
        """Send notification when no withdrawal key is configured"""
        try:
            balance_formatted = format_kraken_balance(balance, asset)
            
            no_key_msg = (
                f"⚠️ KRAKEN WITHDRAWAL KEY MISSING ⚠️\n"
                f"═══════════════════════════════════════\n\n"
                f"🏦 Exchange: Kraken\n"
                f"💎 Asset: {asset}\n"
                f"💰 Balance: {balance_formatted}\n"
                f"🔑 Issue: No withdrawal address configured\n"
                f"💡 Action: Configure withdrawal address in Kraken account"
            )
            
            send_telegram_message(no_key_msg)
            
        except Exception as e:
            logging.error(f"Error sending no withdrawal key notification: {e}")
    
    async def send_insufficient_for_fee_notification(self, asset: str, balance: float, fee: float):
        """Send notification when balance is insufficient to cover withdrawal fee"""
        try:
            balance_formatted = format_kraken_balance(balance, asset)
            fee_formatted = format_kraken_balance(fee, asset)
            
            insufficient_msg = (
                f"⚠️ INSUFFICIENT FOR WITHDRAWAL FEE ⚠️\n"
                f"═══════════════════════════════════════\n\n"
                f"🏦 Exchange: Kraken\n"
                f"💎 Asset: {asset}\n"
                f"💰 Balance: {balance_formatted}\n"
                f"💸 Fee: {fee_formatted}\n"
                f"💡 Action: Waiting for more funds"
            )
            
            send_telegram_message(insufficient_msg)
            
        except Exception as e:
            logging.error(f"Error sending insufficient fee notification: {e}")
    
    def is_withdrawal_on_cooldown(self, cooldown_key: str) -> bool:
        """Check if withdrawal is on cooldown for this asset/account combination"""
        if cooldown_key not in self.withdrawal_cooldown:
            return False

        cooldown_time = 300  # 5 minutes cooldown
        return time.time() - self.withdrawal_cooldown[cooldown_key] < cooldown_time

    def set_withdrawal_cooldown(self, cooldown_key: str):
        """Set withdrawal cooldown for an asset/account combination"""
        self.withdrawal_cooldown[cooldown_key] = time.time()
    
    def get_balance_summary(self) -> str:
        """Get formatted summary of all Kraken balances across all accounts"""
        if not self.clients:
            return "❌ No Kraken accounts configured"

        try:
            summary_lines = ["🏦 KRAKEN MULTI-ACCOUNT BALANCES 🏦", "═══════════════════════════════════════"]
            grand_total_usd = 0

            for client in self.clients:
                try:
                    balances = client.get_account_balance()
                    if not balances:
                        summary_lines.append(f"\n❌ {client.account_name}: Failed to get balances")
                        continue

                    if not balances:
                        summary_lines.append(f"\n💰 {client.account_name}: No balances")
                        continue

                    summary_lines.append(f"\n🏦 {client.account_name}:")
                    summary_lines.append("─" * 30)

                    account_total_usd = 0
                    significant_balances = []

                    for asset, balance in sorted(balances.items()):
                        balance_formatted = format_kraken_balance(balance, asset)
                        usd_value = get_usd_value(balance, asset)

                        if usd_value and usd_value >= 1.0:  # Only show balances worth $1+
                            significant_balances.append((asset, balance_formatted, usd_value))
                            account_total_usd += usd_value

                    if significant_balances:
                        for asset, balance_formatted, usd_value in significant_balances:
                            status = "🟢" if usd_value >= KRAKEN_USD_MINIMUM else "🟡"
                            summary_lines.append(f"{status} {asset}: {balance_formatted} (${usd_value:,.2f})")

                        summary_lines.append(f"💰 Account Total: ${account_total_usd:,.2f}")
                        grand_total_usd += account_total_usd
                    else:
                        summary_lines.append("💰 No significant balances")

                except Exception as e:
                    summary_lines.append(f"\n❌ {client.account_name}: Error - {str(e)}")

            summary_lines.append("\n" + "═" * 40)
            summary_lines.append(f"💎 GRAND TOTAL: ${grand_total_usd:,.2f}")
            summary_lines.append(f"💵 Threshold: ${KRAKEN_USD_MINIMUM:,.0f} minimum per asset")
            summary_lines.append("🟢 = Above threshold | 🟡 = Below threshold")

            return "\n".join(summary_lines)

        except Exception as e:
            logging.error(f"Error getting balance summary: {e}")
            return f"❌ Error getting balances: {e}"

# Global Kraken watcher instance
kraken_watcher = KrakenWatcher()
