import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, Optional, Set
from kraken_client import KrakenAPIClient, format_kraken_balance, get_usd_value
from config import KRAKEN_MINIMUM_BALANCES, KRAKEN_WITHDRAWAL_KEYS, BTC_DESTINATION_WALLET
from utils import send_telegram_message

class KrakenWatcher:
    """
    Enhanced Kraken watcher that monitors ALL balances and auto-withdraws
    """
    
    def __init__(self):
        self.client = KrakenAPIClient()
        self.last_balances = {}
        self.notified_balances = set()  # Track which balances we've already notified about
        self.withdrawal_cooldown = {}   # Track withdrawal cooldowns per asset
        
    async def check_all_balances(self):
        """Check all Kraken balances and trigger withdrawals if needed"""
        try:
            balances = self.client.get_account_balance()
            if not balances:
                logging.warning("Failed to get Kraken balances")
                return
            
            # Process each balance
            for asset, balance in balances.items():
                await self.process_asset_balance(asset, balance)
                
            # Update last known balances
            self.last_balances = balances.copy()
            
        except Exception as e:
            logging.error(f"Error checking Kraken balances: {e}")
    
    async def process_asset_balance(self, asset: str, balance: float):
        """Process individual asset balance"""
        try:
            # Get minimum balance threshold
            min_balance = KRAKEN_MINIMUM_BALANCES.get(asset, KRAKEN_MINIMUM_BALANCES.get('DEFAULT', 0.001))
            
            # Check if balance exceeds minimum
            if balance >= min_balance:
                await self.handle_balance_detected(asset, balance)
            else:
                # Remove from notified set if balance dropped below threshold
                self.notified_balances.discard(asset)
                
        except Exception as e:
            logging.error(f"Error processing {asset} balance: {e}")
    
    async def handle_balance_detected(self, asset: str, balance: float):
        """Handle when a balance exceeds minimum threshold"""
        try:
            balance_key = f"{asset}_{balance:.8f}"
            
            # Check if we've already notified about this balance
            if balance_key not in self.notified_balances:
                await self.send_balance_notification(asset, balance)
                self.notified_balances.add(balance_key)
            
            # Check withdrawal cooldown
            if self.is_withdrawal_on_cooldown(asset):
                return
            
            # Attempt automatic withdrawal
            await self.attempt_withdrawal(asset, balance)
            
        except Exception as e:
            logging.error(f"Error handling {asset} balance: {e}")
    
    async def send_balance_notification(self, asset: str, balance: float):
        """Send balance detection notification"""
        try:
            balance_formatted = format_kraken_balance(balance, asset)
            usd_value = get_usd_value(balance, asset)
            usd_text = f" (${usd_value:,.2f})" if usd_value else ""
            
            notification_msg = (
                f"💰 KRAKEN BALANCE DETECTED 💰\n"
                f"═══════════════════════════════\n\n"
                f"🏦 Exchange: Kraken\n"
                f"💎 Asset: {asset}\n"
                f"💰 Balance: {balance_formatted}{usd_text}\n"
                f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                f"🎯 Status: Checking withdrawal options..."
            )
            
            send_telegram_message(notification_msg)
            logging.info(f"Kraken balance detected: {balance_formatted} {asset}")
            
        except Exception as e:
            logging.error(f"Error sending balance notification: {e}")
    
    async def attempt_withdrawal(self, asset: str, balance: float):
        """Attempt to withdraw the asset"""
        try:
            # Check if we have a withdrawal key configured
            withdrawal_key = KRAKEN_WITHDRAWAL_KEYS.get(asset)
            if not withdrawal_key:
                await self.send_no_withdrawal_key_notification(asset, balance)
                return
            
            # Get withdrawal info to check fees
            withdrawal_info = self.client.get_withdrawal_info(asset, withdrawal_key, balance)
            if not withdrawal_info:
                await self.send_withdrawal_error_notification(asset, balance, "Failed to get withdrawal info")
                return
            
            # Calculate amount after fees
            fee = float(withdrawal_info.get('fee', 0))
            amount_to_withdraw = balance - fee
            
            if amount_to_withdraw <= 0:
                await self.send_insufficient_for_fee_notification(asset, balance, fee)
                return
            
            # Perform withdrawal
            result = self.client.withdraw_funds(asset, withdrawal_key, amount_to_withdraw)
            
            if result:
                await self.send_withdrawal_success_notification(asset, amount_to_withdraw, fee, result)
                self.set_withdrawal_cooldown(asset)
            else:
                await self.send_withdrawal_error_notification(asset, balance, "Withdrawal request failed")
                
        except Exception as e:
            logging.error(f"Error attempting withdrawal for {asset}: {e}")
            await self.send_withdrawal_error_notification(asset, balance, str(e))
    
    async def send_withdrawal_success_notification(self, asset: str, amount: float, fee: float, result: Dict):
        """Send successful withdrawal notification"""
        try:
            amount_formatted = format_kraken_balance(amount, asset)
            fee_formatted = format_kraken_balance(fee, asset)
            usd_value = get_usd_value(amount, asset)
            usd_text = f" (${usd_value:,.2f})" if usd_value else ""
            
            # Get destination based on asset
            if asset in ['BTC', 'XBT']:
                destination = BTC_DESTINATION_WALLET
            else:
                destination = "Configured withdrawal address"
            
            success_msg = (
                f"🚨 KRAKEN WITHDRAWAL SUCCESSFUL 🚨\n"
                f"═══════════════════════════════════════\n\n"
                f"⚡ AUTO-WITHDRAWAL EXECUTED\n\n"
                f"🏦 Exchange: Kraken\n"
                f"💎 Asset: {asset}\n"
                f"💰 Amount: {amount_formatted}{usd_text}\n"
                f"💸 Fee: {fee_formatted}\n"
                f"🎯 To: {destination}\n"
                f"🔗 Ref ID: {result.get('refid', 'N/A')}\n"
                f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                f"✅ Status: Funds secured successfully!"
            )
            
            send_telegram_message(success_msg)
            logging.info(f"Kraken withdrawal successful: {amount_formatted} {asset}")
            
        except Exception as e:
            logging.error(f"Error sending withdrawal success notification: {e}")
    
    async def send_withdrawal_error_notification(self, asset: str, balance: float, error: str):
        """Send withdrawal error notification"""
        try:
            balance_formatted = format_kraken_balance(balance, asset)
            
            error_msg = (
                f"❌ KRAKEN WITHDRAWAL ERROR ❌\n"
                f"═══════════════════════════════════\n\n"
                f"🏦 Exchange: Kraken\n"
                f"💎 Asset: {asset}\n"
                f"💰 Balance: {balance_formatted}\n"
                f"🚫 Error: {error}\n"
                f"🔄 Status: Will retry on next check"
            )
            
            send_telegram_message(error_msg)
            
        except Exception as e:
            logging.error(f"Error sending withdrawal error notification: {e}")
    
    async def send_no_withdrawal_key_notification(self, asset: str, balance: float):
        """Send notification when no withdrawal key is configured"""
        try:
            balance_formatted = format_kraken_balance(balance, asset)
            
            no_key_msg = (
                f"⚠️ KRAKEN WITHDRAWAL KEY MISSING ⚠️\n"
                f"═══════════════════════════════════════\n\n"
                f"🏦 Exchange: Kraken\n"
                f"💎 Asset: {asset}\n"
                f"💰 Balance: {balance_formatted}\n"
                f"🔑 Issue: No withdrawal address configured\n"
                f"💡 Action: Configure withdrawal address in Kraken account"
            )
            
            send_telegram_message(no_key_msg)
            
        except Exception as e:
            logging.error(f"Error sending no withdrawal key notification: {e}")
    
    async def send_insufficient_for_fee_notification(self, asset: str, balance: float, fee: float):
        """Send notification when balance is insufficient to cover withdrawal fee"""
        try:
            balance_formatted = format_kraken_balance(balance, asset)
            fee_formatted = format_kraken_balance(fee, asset)
            
            insufficient_msg = (
                f"⚠️ INSUFFICIENT FOR WITHDRAWAL FEE ⚠️\n"
                f"═══════════════════════════════════════\n\n"
                f"🏦 Exchange: Kraken\n"
                f"💎 Asset: {asset}\n"
                f"💰 Balance: {balance_formatted}\n"
                f"💸 Fee: {fee_formatted}\n"
                f"💡 Action: Waiting for more funds"
            )
            
            send_telegram_message(insufficient_msg)
            
        except Exception as e:
            logging.error(f"Error sending insufficient fee notification: {e}")
    
    def is_withdrawal_on_cooldown(self, asset: str) -> bool:
        """Check if withdrawal is on cooldown for this asset"""
        if asset not in self.withdrawal_cooldown:
            return False
        
        cooldown_time = 300  # 5 minutes cooldown
        return time.time() - self.withdrawal_cooldown[asset] < cooldown_time
    
    def set_withdrawal_cooldown(self, asset: str):
        """Set withdrawal cooldown for an asset"""
        self.withdrawal_cooldown[asset] = time.time()
    
    def get_balance_summary(self) -> str:
        """Get formatted summary of all Kraken balances"""
        try:
            balances = self.client.get_account_balance()
            if not balances:
                return "❌ Failed to get Kraken balances"
            
            if not balances:
                return "💰 No balances found on Kraken"
            
            summary_lines = ["🏦 KRAKEN BALANCES 🏦", "═══════════════════════════"]
            
            total_usd = 0
            for asset, balance in sorted(balances.items()):
                balance_formatted = format_kraken_balance(balance, asset)
                usd_value = get_usd_value(balance, asset)
                
                if usd_value:
                    summary_lines.append(f"💎 {asset}: {balance_formatted} (${usd_value:,.2f})")
                    total_usd += usd_value
                else:
                    summary_lines.append(f"💎 {asset}: {balance_formatted}")
            
            if total_usd > 0:
                summary_lines.append(f"\n💰 Total Value: ~${total_usd:,.2f}")
            
            return "\n".join(summary_lines)
            
        except Exception as e:
            logging.error(f"Error getting balance summary: {e}")
            return f"❌ Error getting balances: {e}"

# Global Kraken watcher instance
kraken_watcher = KrakenWatcher()
