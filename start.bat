@echo off
echo 🚀 Starting Predator Wallet Monitor in Docker...

REM Check if Dock<PERSON> is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    echo Visit: https://docs.docker.com/desktop/windows/
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist .env (
    echo ❌ .env file not found. Please create .env file with your configuration.
    pause
    exit /b 1
)

REM Create logs directory if it doesn't exist
if not exist logs mkdir logs

echo 🔧 Building Docker image...

REM Build and start the container
docker-compose up --build -d

if %errorlevel% equ 0 (
    echo ✅ Predator Wallet Monitor is now running in Docker!
    echo.
    echo 📋 Useful commands:
    echo   View logs:     docker-compose logs -f
    echo   Stop:          docker-compose down
    echo   Restart:       docker-compose restart
    echo   Status:        docker-compose ps
    echo   Shell access:  docker-compose exec predator-wallet-monitor bash
    echo.
    echo 🔍 Checking container status...
    docker-compose ps
    echo.
    echo 📊 Recent logs:
    docker-compose logs --tail=20
) else (
    echo ❌ Failed to start the container. Check the logs for details.
    docker-compose logs
)

pause
