from telegram.ext import <PERSON><PERSON><PERSON><PERSON>, CommandHandler, <PERSON>text<PERSON><PERSON><PERSON>, MessageHandler, filters
from telegram import Update
from utils import (
    init_web3,
    load_wallets,
    add_wallet,
    format_eth_amount
)
from config import TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, NETWORKS
import logging

# Set up logging to file and console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    handlers=[
        logging.FileHandler("bot.log"),
        logging.StreamHandler()
    ]
)

async def log_and_reply(update, message, level="info"):
    user = update.effective_user
    log_msg = f"[{user.id} - {user.username}] {message}"
    if level == "error":
        logging.error(log_msg)
    else:
        logging.info(log_msg)
    await update.message.reply_text(message)

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /start command"""
    await log_and_reply(update,
        "🦁 Welcome to Predator Wallet Monitor!\n\n"
        "Commands:\n"
        "/status - Check all wallet balances\n"
        "/wallets - List all loaded wallets\n"
        "/addwallet <private_key> <name> - Add a new wallet\n"
        "/addmnemonic <12-word-seed> <name> - Add wallet from mnemonic\n"
        "/ping - Check if bot is alive\n"
        "/help - Show this help message"
    )

async def ping(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /ping command"""
    await log_and_reply(update, "✅ Bot is alive!")

async def wallets_cmd(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /wallets command"""
    wallets = load_wallets()
    if not wallets:
        await log_and_reply(update, "No wallets loaded!", level="error")
        return
    msg = "Loaded wallets:\n\n"
    for w in wallets:
        msg += f"Name: {w['name']}\nAddress: {w['address']}\n\n"
    await log_and_reply(update, msg)

async def status(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /status command"""
    logging.info(f"/status called by {update.effective_user.id}")
    if str(update.message.chat_id) != TELEGRAM_CHAT_ID:
        await log_and_reply(update, "Unauthorized", level="error")
        return
    
    wallets = load_wallets()
    
    if not wallets:
        await log_and_reply(update, "No wallets configured!", level="error")
        return
    
    status_msg = "📊 Wallet Status (all networks):\n\n"
    
    for wallet in wallets:
        status_msg += f"Name: {wallet['name']}\nAddress: {wallet['address']}\n"
        for network in NETWORKS:
            try:
                web3 = init_web3(network["rpc_url"])
                balance = web3.eth.get_balance(wallet['address'])
                status_msg += f"{network['name']}: {format_eth_amount(balance)}\n"
            except Exception as e:
                err = f"{network['name']}: Error: {str(e)}"
                status_msg += err + "\n"
                logging.error(err)
        status_msg += "\n"
    
    await log_and_reply(update, status_msg)

async def add_wallet_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /addwallet command"""
    logging.info(f"/addwallet called by {update.effective_user.id}")
    if str(update.message.chat_id) != TELEGRAM_CHAT_ID:
        await log_and_reply(update, "Unauthorized", level="error")
        return
    
    if len(context.args) < 1:
        await log_and_reply(update, "Usage: /addwallet <private_key> <name>", level="error")
        return
    
    private_key = context.args[0]
    name = context.args[1] if len(context.args) > 1 else ""
    
    try:
        if add_wallet(private_key, name):
            await log_and_reply(update, f"✅ Wallet added successfully! Name: {name}")
        else:
            await log_and_reply(update, "❌ Failed to add wallet!", level="error")
    except Exception as e:
        await log_and_reply(update, f"❌ Error: {str(e)}", level="error")

async def add_mnemonic_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /addmnemonic command"""
    logging.info(f"/addmnemonic called by {update.effective_user.id}")
    if str(update.message.chat_id) != TELEGRAM_CHAT_ID:
        await log_and_reply(update, "Unauthorized", level="error")
        return
    
    if len(context.args) < 12:
        await log_and_reply(update, "Usage: /addmnemonic <12-word-seed> <name>", level="error")
        return
    
    mnemonic = " ".join(context.args[:12])
    name = " ".join(context.args[12:]) if len(context.args) > 12 else ""
    
    from utils import derive_private_key_from_mnemonic
    priv, addr = derive_private_key_from_mnemonic(mnemonic)
    
    if not priv or not addr:
        await log_and_reply(update, "❌ Invalid mnemonic!", level="error")
        return
    
    from utils import save_wallets, load_wallets
    wallets = load_wallets()
    wallets.append({"address": addr, "private_key": priv, "name": name})
    
    if save_wallets(wallets):
        await log_and_reply(update, f"✅ Wallet from mnemonic added!\nAddress: {addr}")
    else:
        await log_and_reply(update, "❌ Failed to add wallet!", level="error")

async def echo_chat_id(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user = update.effective_user
    chat_id = update.message.chat_id
    msg = f"[DEBUG] Received message from user {user.id} (username: {user.username}) in chat {chat_id}.\nYour TELEGRAM_CHAT_ID in .env is: {TELEGRAM_CHAT_ID}"
    logging.info(msg)
    await update.message.reply_text(msg)

def main():
    """Start the bot"""
    app = ApplicationBuilder().token(TELEGRAM_BOT_TOKEN).build()
    
    app.add_handler(CommandHandler("start", start))
    app.add_handler(CommandHandler("help", start))
    app.add_handler(CommandHandler("status", status))
    app.add_handler(CommandHandler("wallets", wallets_cmd))
    app.add_handler(CommandHandler("addwallet", add_wallet_command))
    app.add_handler(CommandHandler("addmnemonic", add_mnemonic_command))
    app.add_handler(CommandHandler("ping", ping))
    app.add_handler(MessageHandler(filters.ALL, echo_chat_id))
    
    logging.info("Starting Telegram bot...")
    app.run_polling()
