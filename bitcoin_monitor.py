import requests
import logging
import time
from typing import Dict, List, Optional
from config import BTC_API_URL, BTC_MINIMUM_BALANCE, BTC_DESTINATION_WALLET

class BitcoinMonitor:
    """
    Bitcoin address monitoring using Blockstream API
    """
    
    def __init__(self):
        self.api_url = BTC_API_URL
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'PredatorWalletMonitor/1.0'
        })
    
    def get_address_balance(self, address: str) -> Optional[Dict[str, float]]:
        """
        Get Bitcoin address balance
        
        Returns:
            Dict with 'confirmed' and 'unconfirmed' balances in BTC
        """
        try:
            response = self.session.get(
                f"{self.api_url}/address/{address}",
                timeout=30
            )
            response.raise_for_status()
            
            data = response.json()
            
            # Convert satoshis to BTC
            confirmed_balance = data.get('chain_stats', {}).get('funded_txo_sum', 0) - \
                              data.get('chain_stats', {}).get('spent_txo_sum', 0)
            
            unconfirmed_balance = data.get('mempool_stats', {}).get('funded_txo_sum', 0) - \
                                data.get('mempool_stats', {}).get('spent_txo_sum', 0)
            
            return {
                'confirmed': confirmed_balance / 100000000,  # Convert satoshis to BTC
                'unconfirmed': unconfirmed_balance / 100000000,
                'total': (confirmed_balance + unconfirmed_balance) / 100000000
            }
            
        except requests.exceptions.RequestException as e:
            logging.error(f"Failed to get Bitcoin balance for {address}: {e}")
            return None
        except Exception as e:
            logging.error(f"Bitcoin balance error for {address}: {e}")
            return None
    
    def get_address_transactions(self, address: str, limit: int = 10) -> Optional[List[Dict]]:
        """Get recent transactions for a Bitcoin address"""
        try:
            response = self.session.get(
                f"{self.api_url}/address/{address}/txs",
                timeout=30
            )
            response.raise_for_status()
            
            transactions = response.json()
            return transactions[:limit] if transactions else []
            
        except requests.exceptions.RequestException as e:
            logging.error(f"Failed to get Bitcoin transactions for {address}: {e}")
            return None
        except Exception as e:
            logging.error(f"Bitcoin transaction error for {address}: {e}")
            return None
    
    def get_utxos(self, address: str) -> Optional[List[Dict]]:
        """Get unspent transaction outputs for an address"""
        try:
            response = self.session.get(
                f"{self.api_url}/address/{address}/utxo",
                timeout=30
            )
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logging.error(f"Failed to get UTXOs for {address}: {e}")
            return None
        except Exception as e:
            logging.error(f"UTXO error for {address}: {e}")
            return None
    
    def estimate_fee(self, target_blocks: int = 6) -> Optional[float]:
        """
        Estimate Bitcoin transaction fee
        
        Args:
            target_blocks: Target confirmation blocks (default: 6)
            
        Returns:
            Fee rate in sat/vB
        """
        try:
            response = self.session.get(
                f"{self.api_url}/fee-estimates",
                timeout=30
            )
            response.raise_for_status()
            
            fee_estimates = response.json()
            
            # Get fee for target blocks or closest available
            target_str = str(target_blocks)
            if target_str in fee_estimates:
                return fee_estimates[target_str]
            
            # Find closest target
            available_targets = [int(k) for k in fee_estimates.keys()]
            closest_target = min(available_targets, key=lambda x: abs(x - target_blocks))
            
            return fee_estimates[str(closest_target)]
            
        except Exception as e:
            logging.error(f"Failed to estimate Bitcoin fee: {e}")
            return 10.0  # Default fallback fee rate
    
    def get_network_stats(self) -> Optional[Dict]:
        """Get Bitcoin network statistics"""
        try:
            response = self.session.get(
                f"{self.api_url}/blocks/tip/height",
                timeout=30
            )
            response.raise_for_status()
            
            current_height = response.json()
            
            return {
                'current_height': current_height,
                'timestamp': int(time.time())
            }
            
        except Exception as e:
            logging.error(f"Failed to get Bitcoin network stats: {e}")
            return None

def format_btc_amount(amount: float) -> str:
    """Format BTC amount for display"""
    if amount >= 1:
        return f"{amount:.8f} BTC"
    elif amount >= 0.001:
        return f"{amount:.6f} BTC"
    elif amount >= 0.00001:
        return f"{amount:.5f} BTC"
    else:
        return f"{amount:.8f} BTC"

def get_btc_usd_value(btc_amount: float) -> Optional[float]:
    """Get USD value of BTC amount"""
    try:
        # In production, use real price API
        # For now, using approximate price
        btc_price = 43000  # Approximate BTC price in USD
        return btc_amount * btc_price
    except Exception:
        return None

def format_btc_with_usd(btc_amount: float) -> str:
    """Format BTC amount with USD value"""
    btc_formatted = format_btc_amount(btc_amount)
    usd_value = get_btc_usd_value(btc_amount)
    
    if usd_value:
        return f"{btc_formatted} (${usd_value:,.2f})"
    else:
        return btc_formatted

def is_valid_btc_address(address: str) -> bool:
    """Basic Bitcoin address validation"""
    if not address:
        return False
    
    # Basic validation for common Bitcoin address formats
    if address.startswith('1') and len(address) >= 26 and len(address) <= 35:
        return True  # Legacy P2PKH
    elif address.startswith('3') and len(address) >= 26 and len(address) <= 35:
        return True  # P2SH
    elif address.startswith('bc1') and len(address) >= 39 and len(address) <= 62:
        return True  # Bech32 (native SegWit)
    
    return False
